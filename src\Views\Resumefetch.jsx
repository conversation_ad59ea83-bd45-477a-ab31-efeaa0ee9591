import React, { useState, useEffect, useRef, uniRef, useMemo } from "react";
import LeftNav from "../Components/LeftNav";
import TitleBar from "../Components/TitleBar";
import { MdFilterAlt } from "react-icons/md";
import Modal from "react-modal";
import { IoMdSearch } from "react-icons/io";
import { MdOutlineYoutubeSearchedFor } from "react-icons/md";
import { BiTransferAlt, BiSort, BiSortUp, BiSortDown } from "react-icons/bi";
import { FaFileDownload } from 'react-icons/fa';
import { FcCheckmark } from "react-icons/fc";
import { TailSpin } from "react-loader-spinner";
import { RiMailCheckFill } from "react-icons/ri";
import { VscClearAll } from "react-icons/vsc";
import { useDispatch } from 'react-redux';
import { setCandidates } from '../store/slices/candidateSlice'; // adjust path as needed
import "../Components/leftnav.css";
import "../Components/titlenav.css";
import "../Views/Dashboard/dashboard.css";
import "../Views/resumesearch.css";
import { toast } from "react-toastify";
import { Bounce } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useSelector } from "react-redux";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Tooltip as ReactTooltip } from "react-tooltip";
import { FaAngleLeft } from "react-icons/fa6";
import { FaAngleRight } from "react-icons/fa6";
import { ThreeDots } from "react-loader-spinner";
import { Hourglass } from "react-loader-spinner";
//import { useSelector } from "react-redux";
import {
  faFileAlt,
} from "@fortawesome/free-solid-svg-icons";


function ResumeFetch() {
  const dispatch = useDispatch();

  const [showItems, setShowItems] = useState([]);
  const [selectedProfile, setSelectedProfile] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [candidatesData, setCandidatesData] = useState([]);

  const [isOpen, setIsOpen] = useState(false);
  const [waitForSubmission, setWaitForSubmission] = useState(false);
   const [waitForSubmissionprofile, setwaitForSubmissionprofile] = useState(false);
  const [showAllCandidates, setShowAllCandidates] = useState(false);
  const [loader, setLoader] = useState(true);

  const [belowCount, setBelowCount] = useState(0);

  const [countItems, setCountItems] = useState(0);
  const [id, setId] = useState(1);
  const [loading, setLoading] = useState(true);


  // search all candidates
  const [lowCount, setLowCount] = useState(0); // Total candidates count

  const [increasItems, setIncreasItems] = useState(0); // Page count
  const [currentPage, setCurrentPage] = useState(1); // Current page
  
  const [selectedSkills, setSelectedSkills] = useState([]);
  const [selectedjobId, setSelectedjobId] = useState([]);
  const [selectedclient, setSelectedclient] = useState([]);

  const notify = () => toast.success("Resume downloaded successfully!");
  const { jobs } = useSelector((state) => state.jobSliceReducer);
  const  candidatesretrive  = useSelector((state) => state.candidateSliceReducer.data);

  const [filteredRows, setFilteredRows] = useState([]);



  //  column Filtering

  const [selectAllDate, setSelectAllDate] = useState(false);
  const [uniqueDataDate, setuniqueDataDate] = useState([]);
  const [dateSelected, setdateSelected] = useState([]);

  const [selectAll, setSelectAll] = useState(false);
  const [uniqueDataNames, setuniqueDataNames] = useState([]);
  const [nameSelected, setNameSelected] = useState([]);

  const [selectAllEmail, setSelectAllEmail] = useState(false);
  const [uniqueDataEmail, setuniqueDataEmail] = useState([]);
  const [emailSelected, setEmailSelected] = useState([]);

  const [selectAllMobile, setSelectAllMobile] = useState(false);
  const [uniqueDataMobile, setuniqueDataMobile] = useState([]);
  const [mobileSelected, setMobileSelected] = useState([]);

  const [selectedShares, setSelectedShares] = useState([]);

  const [selectAllClient, setSelectAllClient] = useState(false);
  const [uniqueDataClient, setuniqueDataClient] = useState([]);
  const [clientSelected, setclientSelected] = useState([]);

  const [selectAllProfile, setSelectAllProfile] = useState(false);
  const [uniqueDataProfile, setuniqueDataProfile] = useState([]);
  const [profileSelected, setprofileSelected] = useState([]);

  const [selectAllSkill, setSelectAllSkill] = useState(false);
  const [uniqueDataSkill, setuniqueDataSkill] = useState([]);
  const [skillSelected, setskillSelected] = useState([]);

  const [selectAllStatus, setSelectAllStatus] = useState(false);
  const [uniqueDataStatus, setuniqueDataStatus] = useState([]);
  const [statusSelected, setstatusSelected] = useState([]);
  const [showDetails, setShowDetails] = useState(false);
  
    const [selectAllMatchpercent, setselectAllMatchpercent] = useState(false);
    const [uniqueDataMatchpercent, setuniqueDataMatchpercent] = useState([]);
    const [matchpercentSelected, setMatchpercentSelected] = useState([]);
    const [searchMatchpercent, setSearchMatchpercent] = useState('');

  const [isDateFiltered, setIsDateFiltered] = useState(false);
  const [isnameFiltered, setIsNameFiltered] = useState(false);

  const [isuseridFiltered, setIsUseridFiltered] = useState(false);
  const [ismobileFiltered, setIsMobileFiltered] = useState(false);
  const [isemailFiltered, setIsEmailFiltered] = useState(false);
  const [isclientFiltered, setIsClientFiltered] = useState(false);
  const [isprofileFiltered, setIsProfileFiltered] = useState(false);
  const [isskillFiltered, setIsSkillFiltered] = useState(false);

  const [isstatusFiltered, setIsStatusFiltered] = useState(false);
    const [ismatchpercentFiltered, setIsMatchpercentFiltered] = useState(false);
  const [isColorFiltered, setIsColorFiltered] = useState(false);

  // Sorting state
  const [sortConfig, setSortConfig] = useState({ key: null, direction: null });

  // Global search state
  // const [globalSearchValue, setGlobalSearchValue] = useState('');

  // Date range popup state
  const [showDateRangePopup, setShowDateRangePopup] = useState(false);
  const [selectedDateRange, setSelectedDateRange] = useState('Last 7 Days');
  const [waitForDataFetch, setWaitForDataFetch] = useState(false);

  // profile transfer
      const [selectedRecruiter, setSelectedRecruiter] = useState("")
  useEffect(() => {

    const closeFilterPop = (e) => {
      const allRefIds = [
        "date_ref",
        "name_ref",
        "email_ref",
        "mobile_ref",
        "client_ref",
        "profile_ref",
        "skills_ref",
        "status_ref",
        "match_percent_ref",

        "date_label_ref",
        "name_label_ref",
        "email_label_ref",
        "mobile_label_ref",
        "client_label_ref",
        "profile_label_ref",
        "skills_label_ref",
        "match_percent_label_ref",
        "status_label_ref",
      ];
      let bool = false;
      for (const ref of allRefIds) {
        if (document.getElementById(ref)?.contains(e.target)) {
          bool = true;
          return;
        }
      }
      if (uniRef?.current?.contains(e.target) || bool) {
      
      } else {
     
        setshowSearchjobassignment((prev) => ({
          ...Object.fromEntries(Object.keys(prev).map((key) => [key, false])),
        }));
      }
    };
    document.addEventListener("click", closeFilterPop);
    return () => {
      document.removeEventListener("click", closeFilterPop);
    };
  }, []);

  const [showSearchjobassignment, setshowSearchjobassignment] = useState({
    showSearchName: false,
    showSearchdate: false,
    // showSearchuserId: false,
    showSearchMobile: false,
    showSearchEmail: false,
    showSearchClient: false,
    showSearchProfile: false,
    showSearchSkill: false,
    showSearchStatus: false,
    showSearchmatchpercent: false,
  });

  const handleOkClick = () => {
    // Reset both pagination states to ensure we always return to the first page
    // when filters are applied, regardless of which pagination mode is active
    setId(1);
    setCurrentPage(1);

    // Clear any selected items when applying filters
    setSelectItems([]);

    // Update filtered rows based on selected filters
    updateFilteredRows({
      dateSelected,
      nameSelected,
      emailSelected,
      mobileSelected,
      clientSelected,
      profileSelected,
      skillSelected,
      statusSelected,
      matchpercentSelected,

      setuniqueDataDate,
      setuniqueDataNames,
      setuniqueDataEmail,
      setuniqueDataMobile,
      setuniqueDataClient,
      setuniqueDataProfile,
      setuniqueDataSkill,
      setuniqueDataStatus,
      setuniqueDataMatchpercent,
      
    });

    // Update filter indicator states
    setIsDateFiltered(dateSelected?.length > 0);
    setIsNameFiltered(nameSelected?.length > 0);
    setIsEmailFiltered(emailSelected?.length > 0);
    setIsMobileFiltered(mobileSelected?.length > 0);
    setIsClientFiltered(clientSelected?.length > 0);
    setIsProfileFiltered(profileSelected?.length > 0);
    setIsSkillFiltered(skillSelected?.length > 0);
    setIsStatusFiltered(statusSelected?.length > 0);
    setIsMatchpercentFiltered(matchpercentSelected?.length > 0);

  
  };

  useEffect(() => {
    // console.log("called handlokclick");
    handleOkClick();
  }, [
    dateSelected,
    nameSelected,

    emailSelected,
    mobileSelected,
    clientSelected,
    profileSelected,
    skillSelected,
    matchpercentSelected,
    statusSelected,
  ]);

  const handleCheckboxChangeForDate = (date_created, event) => {
    // Stop event propagation to prevent the popup from closing
    if (event) {
      event.stopPropagation();
    }

    if (!date_created) return;

    const isSelected = dateSelected.includes(date_created.toString());
    if (isSelected) {
      setdateSelected((prev) => {
        return prev.filter((d) => d !== date_created.toString());
      });
      setSelectAllDate(false);
    } else {
      setdateSelected((prev) => {
        return [...prev, date_created.toString()];
      });

      setSelectAllDate(dateSelected.length === uniqueDataDate.length - 1);
    }
  };

  const handleSelectAllForDate = (event) => {
    // Stop event propagation to prevent the popup from closing
    if (event) {
      event.stopPropagation();
    }

    const allChecked = !selectAllDate;
    setSelectAllDate(allChecked);

    if (allChecked) {
      setdateSelected(uniqueDataDate.map((d) => d.toString()));
    } else {
      setdateSelected([]);
    }
  };

  const handleCheckboxChange = (name, event) => {
    // Stop event propagation to prevent the popup from closing
    if (event) {
      event.stopPropagation();
    }

    const isSelected = nameSelected.includes(name);
    if (isSelected) {
      setNameSelected((prevSelected) =>
        prevSelected.filter((item) => item !== name),
      );
      setSelectAll(false);
    } else {
      setNameSelected((prevSelected) => [...prevSelected, name]);
      setSelectAll(nameSelected.length === uniqueDataNames.length - 1);
    }
  };
  const handleSelectAllForName = (event) => {
    // Stop event propagation to prevent the popup from closing
    if (event) {
      event.stopPropagation();
    }

    const allChecked = !selectAll;
    setSelectAll(allChecked);

    if (allChecked) {
      setNameSelected(uniqueDataNames.map((d) => d?.toLowerCase()));
    } else {
      setNameSelected([]);
    }
  };
  const handleCheckboxChangeClient = (client, event) => {
    // Stop event propagation to prevent the popup from closing
    if (event) {
      event.stopPropagation();
    }

    if (!client) return;
    const isSelected = clientSelected.includes(client);
    if (isSelected) {
      setclientSelected((prevSelected) =>
        prevSelected.filter((item) => item !== client),
      );
      setSelectAllClient(false);
    } else {
      setclientSelected((prevSelected) => [...prevSelected, client]);
      setSelectAllClient(clientSelected.length === uniqueDataClient.length - 1);
    }
  };
  const handleSelectAllForClient = (event) => {
    if (event) {
      event.stopPropagation();
    }

    const allChecked = !selectAllClient;
    setSelectAllClient(allChecked);

    if (allChecked) {
      setclientSelected(uniqueDataClient.map((d) => d?.toLowerCase()));
    } else {
      setclientSelected([]);
    }
  };

  const handleCheckboxChangeProfile = (profile, event) => {
    if (event) {
      event.stopPropagation();
    }
    if (!profile) return;
    const isSelected = profileSelected.includes(profile);
    if (isSelected) {
      setprofileSelected((prevSelected) =>
        prevSelected.filter((item) => item !== profile),
      );
      setSelectAllProfile(false);
    } else {
      setprofileSelected((prevSelected) => [...prevSelected, profile]);
      setSelectAllProfile(
        profileSelected.length === uniqueDataProfile.length - 1,
      );
    }
  };
  const handleSelectAllForProfile = (event) => {
    if (event) {
      event.stopPropagation();
    }

    const allChecked = !selectAllProfile;
    setSelectAllProfile(allChecked);

    if (allChecked) {
      setprofileSelected(uniqueDataProfile.map((d) => d?.toLowerCase()));
    } else {
      setprofileSelected([]);
    }
  };

  const handleCheckboxChangeSkill = (skills, event) => {
    if (event) {
      event.stopPropagation();
    }

    const isSelected = skillSelected.includes(skills);
    if (isSelected) {
      setskillSelected((prevSelected) =>
        prevSelected.filter((item) => item !== skills),
      );
      setSelectAllSkill(false);
    } else {
      setskillSelected((prevSelected) => [...prevSelected, skills]);
      setSelectAllSkill(skillSelected.length === uniqueDataSkill.length - 1);
    }
  };
  const handleSelectAllForSkill = (event) => {
    if (event) {
      event.stopPropagation();
    }

    const allChecked = !selectAllSkill;
    setSelectAllSkill(allChecked);

    if (allChecked) {
      setskillSelected(uniqueDataSkill.map((d) => d?.toLowerCase()));
    } else {
      setskillSelected([]);
    }
  };
  const handleCheckboxChangeStatus = (status, event) => {
    if (event) {
      event.stopPropagation();
    }

    const isSelected = statusSelected.includes(status);
    if (isSelected) {
      setstatusSelected((prevSelected) =>
        prevSelected.filter((item) => item !== status),
      );
      setSelectAllStatus(false);
    } else {
      setstatusSelected((prevSelected) => [...prevSelected, status]);
      setSelectAllStatus(statusSelected.length === uniqueDataStatus.length - 1);
    }
  };
  const handleSelectAllForStatus = (event) => {
    if (event) {
      event.stopPropagation();
    }

    const allChecked = !selectAllStatus;
    setSelectAllStatus(allChecked);

    if (allChecked) {
      setstatusSelected(uniqueDataStatus.map((d) => d?.toLowerCase()));
    } else {
      setstatusSelected([]);
    }
  };

  // Define the match percentage ranges
const matchPercentRanges = [
  { label: "85% - 100%", min: 0.85, max: 1.0 },
  { label: "60% - 85%", min: 0.60, max: 0.85 },
  { label: "50% - 60%", min: 0.50, max: 0.60 },
];


    const handleCheckboxMatchpercent = (match_score,event) => {
      if (event) {
      event.stopPropagation();
    }
    const isSelected = matchpercentSelected.includes(match_score);
    if (isSelected) {
      setMatchpercentSelected((prevSelected) =>
        prevSelected.filter((item) => item !== match_score),
      );
      setselectAllMatchpercent(false);
    } else {
      setMatchpercentSelected((prevSelected) => [...prevSelected, match_score]);
      setselectAllMatchpercent(matchpercentSelected.length === uniqueDataMatchpercent.length - 1);
    }
  };

const handleSelectAllMatchpercent = () => {
  const allChecked = !selectAllMatchpercent;
  setselectAllMatchpercent(allChecked);
  if (allChecked) {
    setMatchpercentSelected(matchPercentRanges.map(r => r.label));
  } else {
    setMatchpercentSelected([]);
  }
};

  // Sorting functionality
  const handleSort = (key) => {
    if (sortConfig.key === key) {
      if (sortConfig.direction === 'asc') {
        // Second click → descending
        setSortConfig({ key, direction: 'desc' });
      } else if (sortConfig.direction === 'desc') {
        // Third click → reset
        setSortConfig({ key: null, direction: null });
      } else {
        // Should not occur, but fallback
        setSortConfig({ key, direction: 'asc' });
      }
    } else {
      // First click on a new column → ascending
      setSortConfig({ key, direction: 'asc' });
    }
    setCurrentPage(1); // Reset pagination
  };

  // Get sort icon based on current sort state
  const getSortIcon = (columnKey) => {
    const isActive = sortConfig.key === columnKey;

    if (!isActive) {
      return <BiSort className="sort-icon" style={{ color: '#fff', fontWeight: '800', fontSize: '16px', marginLeft: '5px' }} />;
    }

    if (sortConfig.direction === 'desc') {
      return <BiSortUp className="sort-icon" style={{ color: 'orange', fontWeight: '800', fontSize: '19px', marginLeft: '5px' }} />;
    }

    if (sortConfig.direction === 'asc') {
      return <BiSortDown className="sort-icon" style={{ color: 'orange', fontWeight: '800', fontSize: '19px', marginLeft: '5px' }} />;
    }

    return <BiSort className="sort-icon" style={{ color: '#fff', fontWeight: '800', fontSize: '16px', marginLeft: '5px' }} />;
  };

  // Global search functionality
  const extractKeyValuePairs = (obj, keys) => {
    const result = {};
    keys.forEach(key => {
      if (obj.hasOwnProperty(key)) {
        result[key] = obj[key];
      }
    });
    return result;
  };

  // Apply global search filter
  // const applyGlobalSearch = (data) => {
  //   if (!globalSearchValue) return data;

  //   return data.filter((item) => {
  //     const extractedObj = extractKeyValuePairs(item, [
  //       "id",
  //       "date_created",
  //       "name",
  //       "email",
  //       "mobile",
  //       "client",
  //       "profile",
  //       "skills",
  //       "status",
  //       "match_score"
  //     ]);

  //     for (const key in extractedObj) {
  //       if (key === "id") continue;

  //       let val = extractedObj[key];
  //       if (val !== null && val !== undefined) {
  //         if (typeof val !== "string") {
  //           val = val.toString();
  //         }
  //         if (val.toLowerCase().includes(globalSearchValue.toLowerCase())) {
  //           return true;
  //         }
  //       }
  //     }
  //     return false;
  //   });
  // };

  // Fetch table data with date range
  const fetchTableData = async (dateRange = selectedDateRange) => {
    setWaitForDataFetch(true);
    try {
      const recruitername = localStorage.getItem("name");
      const response = await fetch("  http://127.0.0.1:5002/fetch_table_data", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          recruiter: recruitername,
          date_range: dateRange
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.candidates) {
        // Update candidates data with fetched data
        dispatch(setCandidates(data.candidates));
        toast.success(`Data retrieved for ${dateRange}`);
      } else {
        toast.info("No candidates found for the selected date range.");
      }
    } catch (error) {
      console.error("Error fetching table data:", error);
      toast.error("An error occurred while fetching data.");
    } finally {
      setWaitForDataFetch(false);
    }
  };

  // Handle date range selection
  const handleDateRangeSelection = (range) => {
    setSelectedDateRange(range);
  };

  // Handle retrieve data confirmation
  const handleRetrieveData = () => {
    fetchTableData(selectedDateRange);
    setShowDateRangePopup(false);
  };

  // Apply sorting and pagination to filtered data
  const sortedAndFilteredData = useMemo(() => {
    // Start with filtered rows (already filtered by column filters)
    let data = filteredRows;

    // Apply global search
    // data = applyGlobalSearch(data);

    // Apply sorting
    if (sortConfig.key && data) {
      data = [...data].sort((a, b) => {
        const aValue = a[sortConfig.key];
        const bValue = b[sortConfig.key];

        if (aValue == null && bValue == null) return 0;
        if (aValue == null) return sortConfig.direction === 'asc' ? 1 : -1;
        if (bValue == null) return sortConfig.direction === 'asc' ? -1 : 1;

        let comparison = 0;

        if (sortConfig.key === 'date_created') {
          // Sort dates
          comparison = new Date(aValue) - new Date(bValue);
        } else if (sortConfig.key === 'mobile' || sortConfig.key === 'match_score') {
          // Sort numbers
          comparison = Number(aValue) - Number(bValue);
        } else {
          // Sort strings (case insensitive)
          const aStr = String(aValue).toLowerCase();
          const bStr = String(bValue).toLowerCase();
          comparison = aStr.localeCompare(bStr);
        }

        return sortConfig.direction === 'asc' ? comparison : -comparison;
      });
    }

    return data;
     }, [filteredRows, sortConfig]);
  // }, [filteredRows, sortConfig, globalSearchValue]);

  // Get paginated data for current page
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * 60;
    const endIndex = startIndex + 60;
    return sortedAndFilteredData.slice(startIndex, endIndex);
  }, [sortedAndFilteredData, currentPage]);

  // Update counts when sorted data changes
  useEffect(() => {
    setLowCount(sortedAndFilteredData.length);
    setBelowCount(sortedAndFilteredData.length);
    setIncreasItems(Math.ceil(sortedAndFilteredData.length / 60));
  }, [sortedAndFilteredData]);

  // email handle ----------------------------------------------------------------------------------

  //   filter of column

  // Removed unused state variable
  const updateFilteredRows = ({
    dateSelected,
    nameSelected,
    mobileSelected,
    emailSelected,
    clientSelected,
    profileSelected,
    skillSelected,
    matchpercentSelected,
    statusSelected,

    setuniqueDataDate,
    setuniqueDataNames,
    setuniqueDataMobile,
    setuniqueDataEmail,
    setuniqueDataClient,
    setuniqueDataProfile,
    setuniqueDataSkill,
    setuniqueDataStatus,
    setuniqueDataMatchpercent,
  }) => {

    let prevfilteredRows = candidatesData;
    if (dateSelected?.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        dateSelected?.includes(row.date_created.toString()),
      );
      // console.log("ifff", dateSelected);
    }

    if (nameSelected?.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        nameSelected?.includes(row.name?.toLowerCase()),
      );
    }
    if (emailSelected?.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        emailSelected.includes(row.email?.toLowerCase()),
      );
    }
    if (mobileSelected?.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        mobileSelected.includes(row.mobile.toString()),
      );
    }
    if (clientSelected?.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        clientSelected.includes(row.client?.toLowerCase()),
      );
    }
    if (profileSelected?.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        profileSelected.includes(row.profile?.toLowerCase()),
      );
    }
    if (skillSelected?.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        skillSelected.includes(row.skills?.toLowerCase()),
      );
    }

    if (statusSelected?.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        statusSelected.includes(row.status?.toLowerCase()),
      );
    }
 if (matchpercentSelected?.length !== 0) {
  prevfilteredRows = prevfilteredRows.filter(row => {
    const score = row.match_score; // decimal value (0 to 1)
    return matchpercentSelected.some(rangeLabel => {
      const range = matchPercentRanges.find(r => r.label === rangeLabel);
      return score >= range.min && score <= range.max;
    });
  });
}

    const arrayNames = [
      "dateSelected",
      "nameSelected",
      "emailSelected",
      "mobileSelected",
      "clientSelected",
      "profileSelected",
      "skillSelected",
      "statusSelected",
      "matchpercentSelected",
    ];

    const arrays = [
      dateSelected,
      nameSelected,
      emailSelected,
      mobileSelected,
      clientSelected,
      profileSelected,
      skillSelected,
      matchpercentSelected,
      statusSelected,
    ];

    // Create an array of filter names that have active selections
    let NamesOfNonEmptyArray = [];
    arrays.forEach((arr, index) => {
      if (arr?.length > 0) {
        NamesOfNonEmptyArray.push(arrayNames[index]);
      }
    });
    if (!NamesOfNonEmptyArray.includes("dateSelected")) {
      setuniqueDataDate(() => {
      
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.date_created;
            }),
          ),
        );
      });
    }
    if (!NamesOfNonEmptyArray.includes("nameSelected")) {
      setuniqueDataNames(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.name;
            }),
          ),
        );
      });
    }

    if (!NamesOfNonEmptyArray.includes("emailSelected")) {
      setuniqueDataEmail(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.email?.trim();
            }),
          ),
        );
      });
    }
    if (!NamesOfNonEmptyArray.includes("mobileSelected")) {
      setuniqueDataMobile(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.mobile;
            }),
          ),
        );
      });
    }

    if (!NamesOfNonEmptyArray.includes("clientSelected")) {
      setuniqueDataClient(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.client?.trim();
            }),
          ),
        );
      });
    }
    if (!NamesOfNonEmptyArray.includes("profileSelected")) {
      setuniqueDataProfile(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.profile?.trim();
            }),
          ),
        );
      });
    }
    if (!NamesOfNonEmptyArray.includes("skillSelected")) {
      setuniqueDataSkill(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.skills?.trim();
            }),
          ),
        );
      });
    }

    if (!NamesOfNonEmptyArray.includes("statusSelected")) {
      setuniqueDataStatus(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.status;
            }),
          ),
        );
      });
    }
    if (!NamesOfNonEmptyArray.includes("matchpercentSelected")) {
      setuniqueDataMatchpercent(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.match_score;
            }),
          ),
        );
      });
    }
    // Update the filtered rows state
    setFilteredRows(prevfilteredRows);

    // Update the count states for pagination
    setLowCount(prevfilteredRows.length);
    setBelowCount(prevfilteredRows.length);

    // Update the number of pages based on the filtered results
    setIncreasItems(Math.ceil(prevfilteredRows.length / 60));
  };


  useEffect(() => {
    // Initialize empty arrays for all selected filters
    setdateSelected([]);
    setNameSelected([]);
    setEmailSelected([]);
    setMobileSelected([]);
    setclientSelected([]);
    setprofileSelected([]);
    setskillSelected([]);
    setstatusSelected([]);
    setMatchpercentSelected([]);

    updateFilteredRows({
      dateSelected: [],
      nameSelected: [],
      emailSelected: [],
      mobileSelected: [],
      clientSelected: [],
      profileSelected: [],
      skillSelected: [],
      statusSelected: [],
      matchpercentSelected: [],

      setuniqueDataDate,
      setuniqueDataNames,
      setuniqueDataMobile,
      setuniqueDataEmail,
      setuniqueDataClient,
      setuniqueDataProfile,
      setuniqueDataSkill,
      setuniqueDataStatus,
      setuniqueDataMatchpercent,
    });
  }, []);
  const profiles = jobs
    ? jobs.map((item) => ({
      name: `${item.role} - ${item.client}`,
      roleid: item.id,
      client: item.client,
      role: item.role,
      JobIds: item.id,
      Jobskill: item.skills,
    }))
    : [];


  const filteredProfiles = profiles.filter((profile) =>
    profile.name.toLowerCase().includes(searchTerm.toLowerCase())
  );
 


  const handleProfileChange = (item) => {
    console.log(item,"selectedjodddddddddddddddddddddddddbid")
    // console.log(roleid,"selectroleids")
    setIsOpen(false);
    // const Jobclient = matchedJobs.map(job => job.client);
    setSelectedProfile(item.role);
    setSearchTerm(item.role);
    setSelectedclient(item.client);

    setSelectedjobId(item.roleid);
    const skillsArray = typeof item.Jobskill === 'string'
    ? item.Jobskill.split(',').map(skill => skill.trim())
    : item.Jobskill;
    console.log(skillsArray," set skils in localstorage")
    setSelectedSkills(skillsArray);

    localStorage.setItem('selectedProfile', item.role);
    localStorage.setItem('selectedjobId', item.roleid);
    localStorage.setItem('selectedSkills', JSON.stringify(skillsArray));
  };

console.log(selectedjobId,"selectedjobid")
  useEffect(() => {
    const storedProfile = localStorage.getItem('selectedProfile');
    const storedjobId = localStorage.getItem('selectedjobId');
    const storedSkills = JSON.parse(localStorage.getItem('selectedSkills') || '[]');
  
    console.log(storedSkills, "storedSkills");
    const storedSearchTerm = localStorage.getItem('searchTerm');

    if (storedProfile) setSelectedProfile(storedProfile);
    if (storedjobId) setSelectedjobId(storedjobId);
   
    if (storedSkills.length > 0) setSelectedSkills(storedSkills);
 
     if (storedProfile) setSearchTerm(storedProfile);
  }, []);

  const { StoredCandidateData } = useSelector((state) => state.storeCandidateSliceReducer);
 

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setIsOpen(true);
    if (e.target.value === "") {
      setShowAllCandidates(true);
    }
    

  };


  const [ hasSubmitted, setHasSubmitted] = useState(false);

  const [warningMessage, setWarningMessage] = useState('');
  const handleSubmit = async () => {
    if (!waitForSubmission) {
      setWaitForSubmission(true);
      setLoading(true);
      setHasSubmitted(true);

      // Reset pagination to first page whenever a new search is performed
      setCurrentPage(1);
      setId(1);

      if (!searchTerm) {
        // If searchTerm is empty, show all candidates
        toast.error("Please enter a job title to search.");
        setLoading(false);
        setShowAllCandidates(true);
        setWaitForSubmission(false);
        return;
      }

      if (!selectedSkills) {
        toast.error("Please enter at least one skill.");
        setLoading(false);
        setWaitForSubmission(false);
        return;
      }

      if (selectedProfile) {
        const skillsString = selectedSkills;
        try {
          const response = await fetch("  http://127.0.0.1:5002/match-candidates", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ job_role: selectedProfile, skills: skillsString }),
          });

          if (response.ok) {
            const data = await response.json();
            // toast.success(data.message);
            const matchedCandidates = data.candidates;

            const filteredCandidates = matchedCandidates.filter(candidate => candidate.client !== selectedclient);
            const excludedCandidates = matchedCandidates.filter(candidate => candidate.client === selectedclient);
            if (filteredCandidates.length > 0) {
              dispatch(setCandidates(filteredCandidates));
              setSelectItems([]);
            } else {
              toast.info("No candidates found matching your search criteria.");
            }
          } else {
            const errorData = await response.json();
            toast.error(errorData.message );
          }
        } catch (error) {
          console.error("Error submitting profile:", error);
          toast.error("An error occurred while searching for candidates.");
        } finally {
          setWaitForSubmission(false);
          setLoading(false);
        }
      }
    }
  };

  useEffect(() => {

    if (candidatesretrive && candidatesretrive.length > 0) {
      setHasSubmitted(true); // Persist submission state if Redux has data
      setLoading(false)
    }
    setCandidatesData(candidatesretrive); // Assign fetched data to state
    setLowCount(candidatesretrive.length);
    setIncreasItems(Math.ceil(candidatesretrive.length / 60));
    setShowAllCandidates(false);

    setBelowCount(candidatesretrive.length);

    setuniqueDataDate([
      ...new Set(
        candidatesretrive.map((d) =>
          new Date(d.date_created).toISOString().split('T')[0]
        )
      ),
    ]);
    setuniqueDataNames([...new Set(candidatesretrive.map((d) => d.name))]);
    setuniqueDataEmail([...new Set(candidatesretrive.map((d) => d.email))]);
    setuniqueDataMobile([...new Set(candidatesretrive.map((d) => d.mobile))]);
    setuniqueDataClient([...new Set(candidatesretrive.map((d) => d.client))]);
    setuniqueDataProfile([...new Set(candidatesretrive.map((d) => d.profile))]);
    setuniqueDataSkill([...new Set(candidatesretrive.map((d) => d.skills))]);
    setuniqueDataStatus([...new Set(candidatesretrive.map((d) => d.status))]);
        setuniqueDataMatchpercent([...new Set(candidatesretrive.map((d) => d.match_score))]);

    setFilteredRows(candidatesretrive);
  }, [candidatesretrive]); // ✅ Add dependencies here

  useEffect(() => {
    const handleClick = (e) => {
      const target = e.target;
      

      const idx = candidatesData.findIndex((item) => {
        return (
          item.id.toString() === target.id.substring(0, target.id.length - 1)
        );
      });
    

      if (idx !== -1) {
        console.log(candidatesData[idx]);

        // Update the state of showItems based on the clicked target
        const update = new Array(candidatesData.length)
          .fill()
          .map((_, index) => ({
            id: candidatesData[index].id.toString(),
            client: false,
            recruiter: false,
            role: false,
          }));

        if (target.id.endsWith("1")) {
          update[idx] = {
            ...showItems[idx],
            client: false,
            skills: !showItems[idx]?.skills,
            role: false,
          };
        }
        console.log(update);
        setShowItems(update);
      } else {
        if (

          target.id === "default1"
        )
          return;

        const initial = new Array(candidatesData.length).fill().map((_, index) => ({
          id: candidatesData[index].id.toString(),
          client: false,
          recruiter: false,
          role: false,
        }));
        setShowItems(initial);
        // console.log("outside");
      }
    };

    window.addEventListener("click", handleClick);
    return () => {
      window.removeEventListener("click", handleClick);
    };
  }, [candidatesData, showItems]);

  const resumeApiCall = async (candidate) => {
    try {
      const response = await fetch(
        `  http://127.0.0.1:5002/view_resume/${candidate.id}`,
        {
          method: "GET",
        }
      );

      if (response.ok) {
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);

        // Open the resume in a new tab for viewing
        window.open(url, "_blank");

        // Optional: Cleanup after some time
        setTimeout(() => URL.revokeObjectURL(url), 10000); // Revoke after 10 seconds
      } else {
        console.log("Failed to fetch resume:", response.statusText);
      }
    } catch (err) {
      console.log("Error fetching resume:", err);
    }
  };


  // page number

  const getPageRange = () => {
    const pageRange = [];
    const maxPagesToShow = 5; // Adjust this value to show more or fewer page numbers

    let startPage = Math.max(1, id - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(countItems, startPage + maxPagesToShow - 1);

    if (endPage - startPage < maxPagesToShow - 1) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    if (startPage > 1) {
      pageRange.push(1);
      if (startPage > 2) {
        pageRange.push("...");
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      pageRange.push(i);
    }

    if (endPage < countItems) {
      if (endPage < countItems - 1) {
        pageRange.push("...");
      }
      pageRange.push(countItems);
    }

    return pageRange;
  };

  const goToPage = (pageNumber) => {
    // Only process numeric page numbers (skip "..." in pagination)
    if (typeof pageNumber === "number" && pageNumber >= 1 && pageNumber <= countItems) {
      setId(pageNumber);
      // Clear any selected items when changing pages to avoid confusion
      setSelectItems([]);
    }
  };
  // search candidates page count

  const getPageRangesearch = () => {
    const pageRange = [];
    const maxPagesToShow = 5;
    const totalPages = Math.ceil(sortedAndFilteredData.length / 60);
    let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);

    if (endPage - startPage < maxPagesToShow - 1) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    if (startPage > 1) {
      pageRange.push(1);
      if (startPage > 2) {
        pageRange.push("...");
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      pageRange.push(i);
    }

    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pageRange.push("...");
      }
      pageRange.push(totalPages);
    }

    return pageRange;
  };

  const goToPagesearch = (pageNumber) => {
    // Only process numeric page numbers (skip "..." in pagination)
    const totalPages = Math.ceil(sortedAndFilteredData.length / 60);
    if (typeof pageNumber === "number" && pageNumber >= 1 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
      // Clear any selected items when changing pages to avoid confusion
      setSelectItems([]);
    }
  };



  const [list, setList] = useState([])


  const [ResumeModal, setResumeModal] = useState(false);




  const [isAnimating, setIsAnimating] = useState(false);


  useEffect(() => {
    setIsAnimating(true);
    setTimeout(() => {
      setIsAnimating(false);
    }, 500); // Adjust the timeout to match your animation duration
  }, [location]);

  const [waitForSubmissionemail, setWaitForSubmissionemail] = useState(false);
  const [selectItems, setSelectItems] = useState([]);
  const handleSelectAll = (e) => {
    if (e.target.checked) {
      // Get only the candidates from the current page
      setSelectItems(paginatedData);
    } else {
      setSelectItems([]);
    }
  };


  const handleSelectIndividual = (id, e) => {
    if (e.target.checked) {
      const selectedCandidate = candidatesData.find(item => item.id === id);
      setSelectItems(prev => [...prev, selectedCandidate]);
    } else {
      setSelectItems(prev => prev.filter(item => item.id !== id));
    }
  };

  const recruitername = localStorage.getItem("name");
  const job_role = searchTerm

  const [emailSubject, setEmailSubject] = useState();
  useEffect(() => {
    if (searchTerm) {
      setEmailSubject(`Exciting Opportunity for the Role of ${searchTerm} `);
    }
  }, [searchTerm]);


  const bodyRef = useRef(null);
  const getEmailBodyContent = () => {
    return bodyRef.current?.innerHTML || "";
  };
  // const placeholderRegex = /\{[a-zA-Z0-9_]+\}/g;
  const [showEmailModal, setShowEmailModal] = useState(false);

  const closeModal = () => {
    setShowEmailModal(false);

  };
  useEffect(() => {
    const handleKeyDown = (e) => {
      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) return;

      const range = selection.getRangeAt(0);
      const { startContainer, startOffset } = range;

      // Get the node before the cursor
      const prevNode =
        startOffset > 0
          ? startContainer.childNodes[startOffset - 1] || startContainer
          : startContainer.previousSibling;

      const nextNode = startContainer.childNodes[startOffset];

      const isDeletingPlaceholder =
        (e.key === 'Backspace' && prevNode?.contentEditable === 'false') ||
        (e.key === 'Delete' && nextNode?.contentEditable === 'false');

      if (isDeletingPlaceholder) {
        e.preventDefault();
      }
    };

    const current = bodyRef.current;
    if (current) {
      current.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      if (current) {
        current.removeEventListener('keydown', handleKeyDown);
      }
    };
  }, []);


  const user_type = localStorage.getItem("user_type");
  const handleEmailSend = async () => {
    if (!waitForSubmissionemail) {
      setWaitForSubmissionemail(true);
      //    console.log(searchTerm,"searchedvalue")
      if (selectItems.length === 0) {
        toast.warn("Please select at least one candidate.");
        return;
      }
      const candidates = selectItems.map(({ resume_base64, date_created, match_score, status, reason, matching_percentage, mobile, id, client, skills, ...rest }) => rest);
      const content = getEmailBodyContent();

      if (selectItems) {
        try {
          const response = await fetch("  http://127.0.0.1:5002/send_emails", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              candidates, job_role, recruitername, job_id: selectedjobId, subject: emailSubject,
              body: content,
            }),
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();

          if (response.ok) {

            setShowEmailModal(false);
            setWaitForSubmissionemail(false);
            toast.success(data.message);
            // toast.success("Mail sended respect to that candidate successfully");
            setSelectItems([]);
            // setSearchTerm('')
            // setEmailBody('')
            // setSelectedSkills('')
          } else {
            toast.error(data.message);
            setShowEmailModal(false);
            setWaitForSubmissionemail(false);
          }
          // fetchAllCandidates()

        } catch (error) {
          console.error("Error submitting profile:", error);
          setWaitForSubmissionemail(false);
        }
      }
    }
  };


  const handledownloadResumes = async () => {
    if (selectItems.length === 0) {
      toast.warn("Please select at least one candidate to download resumes.");
      return;
    }
    const candidateIds = selectItems.map(item => item.id);
    try {
      const response = await fetch(

        "  http://127.0.0.1:5002/download_resumess",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ candidate_ids: candidateIds }),
        },
      );
      if (response.ok) {

        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        window.open(url, "_blank");
    
        setSelectItems([]);
      } else {
        console.log("Failed to fetch jd_file:", response.statusText);
      }
    } catch (err) {
      console.log("Error fetching jd_file:", err);
    }
  };


  const statusColorMapping = {
    "selected": "green",
    "boarded": "green",
    "rejected": "red",
    "hold": "red",
    "drop": "red",
    "duplicate": "red",
    "show": "red",
  };


  const getStatusColor = (status) => {
    const statusLowerCase = status.toLowerCase();
    if (statusColorMapping.hasOwnProperty(statusLowerCase)) {
      return statusColorMapping[statusLowerCase];
    }
    if (statusLowerCase.includes("selected") ||
      statusLowerCase.includes("boarded")) {
      return "green";
    } else if (
      statusLowerCase.includes("rejected") ||
      statusLowerCase.includes("hold") ||
      statusLowerCase.includes("drop") ||
      statusLowerCase.includes("duplicate") ||
      statusLowerCase.includes("show")
    ) {
      return "red";
    } else {
      return "orange";
    }
  };

  
    const [dateRangeSelected, setDateRangeSelected] = useState([]);
    const handleDateRangeCheckbox = (label, days) => {
      const today = new Date();
      const startDate = new Date();
      startDate.setDate(today.getDate() - days);
  
      const filteredDates = uniqueDataDate.filter((d) => {
        const date = new Date(d);
        return date >= startDate && date <= today;
      });
      console.log(filteredDates, "filteredDates");
      const isAlreadySelected = dateRangeSelected.includes(label);
  
      if (isAlreadySelected) {
        // Remove label and the range dates from selection
        setDateRangeSelected(prev =>
          prev.filter(d => ![label, ...filteredDates].includes(d))
        );
        setdateSelected(prev => prev.filter(d => !filteredDates.includes(d)));
      } else {
        // Add label and range dates
        setDateRangeSelected(prev => [...new Set([...prev, label, ...filteredDates])]);
        setdateSelected(prev => [...new Set([...prev, ...filteredDates])]);
      }
    };


    const [showCustomDatePopup, setShowCustomDatePopup] = useState(false);
    const [fromDate, setFromDate] = useState("");
    const [toDate, setToDate] = useState("");
    const handleCustomDateFilter = () => {
      if (!fromDate || !toDate) return;
  
      const from = new Date(fromDate);
      const to = new Date(toDate);
  
      const filtered = uniqueDataDate.filter((d) => {
        const date = new Date(d);
        return date >= from && date <= to;
      });
  
      setdateSelected((prev) => [...new Set([...prev, ...filtered])]);
    };

      const [showprofiletransfer, setShowProfileTransfer] = useState(false);
  const closeProfileTransfer = () => {
    setShowProfileTransfer(false);  // Close modal
  };
  
  // profil transfer
const { recruiters, managers } = useSelector((state) => state.userSliceReducer);
  const allUsers = [...(recruiters), ...(managers)];

    const loggedInUsername = localStorage.getItem("username");
    const reviewerListToShow = allUsers.filter(user => user.username !== loggedInUsername);



    const styles = {
  modal: {
    overlay: {
      backgroundColor: 'rgba(0, 0, 0, 0.75)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
    },
    content: {
      position: 'relative',
      top: 'auto',
      left: 'auto',
      right: 'auto',
      bottom: 'auto',
      width: 'clamp(300px, 40vw, 450px)', // Responsive width
      background: '#ffffff',
      borderRadius: '12px',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.25)',
      padding: '0', // Reset padding to handle it with internal elements
      border: 'none',
      overflow: 'hidden', // Ensures the border radius is respected by children
    },
  },
  form: {
    display: 'flex',
    flexDirection: 'column',
    gap: '24px', // Space between header, content, and actions
    padding: '24px',
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    margin: 0,
    fontSize: '22px',
    fontWeight: '600',
    color: '#333',
  },
  closeButton: {
    background: 'transparent',
    border: 'none',
    fontSize: '28px',
    lineHeight: 1,
    cursor: 'pointer',
    color: '#888',
  },
  content: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px', // Space between label and select
  },
  label: {
    fontWeight: '500',
    color: '#444',
    fontSize: '14px',
  },

  actions: {
    display: 'flex',
    justifyContent: 'flex-end',
    gap: '12px',
  },
  cancelButton: {
    padding: '10px 20px',
    borderRadius: '8px',
    border: '1px solid #ccc',
    backgroundColor: '#fff',
    color: '#333',
    fontSize: '14px',
    fontWeight: '600',
    cursor: 'pointer',
  },
  submitButton: {
    padding: '10px 20px',
    borderRadius: '8px',
    border: 'none',
    backgroundColor: '#32406d',
    color: '#ffffff',
    fontSize: '14px',
    fontWeight: '600',
    cursor: 'pointer',
  },
  submitButtonDisabled: {
    backgroundColor: '#a0a0a0',
    cursor: 'not-allowed',
  },
};


 
 const handleTransfer = async () => {
    if (!waitForSubmissionprofile) {
      setwaitForSubmissionprofile(true);
      //    console.log(searchTerm,"searchedvalue")
      if (selectItems.length === 0) {
        toast.warn("Please select at least one candidate.");
        return;
      }
      const candidates = selectItems.map(({ resume_base64, date_created, match_score, status, reason, matching_percentage, mobile,email,name, client, skills, ...rest }) => rest);
      const content = getEmailBodyContent();

      if (selectItems) {
        try {
          const response = await fetch("  http://127.0.0.1:5002/assign_candidate_portal", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              candidates, recruitername,selectedRecruiter
              // body: content,
            }),
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();

          if (response.ok) {

            setShowProfileTransfer(false);
            setwaitForSubmissionprofile(false);
            toast.success(data.message);
           
            setSelectItems([]);
        
          } else {
            toast.error(data.message);
            setShowProfileTransfer(false);
            setwaitForSubmissionprofile(false);
          }
          // fetchAllCandidates()

        } catch (error) {
          console.error("Error submitting profile:", error);
          setwaitForSubmissionprofile(false);
        }
      }
    }
  };
  return (

    <div className="wrapper">
      <LeftNav />
      <div className="section">
        <TitleBar />

        <div
          className="mobiledash useraccco"
          style={{ gap: "10px" }}
        >
          <label
            style={{
              marginTop: "1vh",
              fontWeight: "500",
              paddingRight: "5px",
            }}
          >
            {/* search */}
          </label>


          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '10px',
            flexWrap: 'wrap',
            zIndex: 2
          }}>
            {/* Header */}
            <h5
              id="theader"
              className="joblisthed"
              style={{
                fontSize: "18px",
                fontWeight: "700",
                marginRight: "auto"
              }}
            >
              Resume Portal
            </h5>
            {/* Tansfer data */}

              <button
                onClick={() => {
                if (selectItems.length > 0) {
                  setShowProfileTransfer(true);
                }
              }}
              style={{
                display: 'flex',
                position: 'relative',
                padding: '3px',
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: "5px",
                border: "none",
                height: '30px',
                backgroundColor: '#f1f1f1',
                cursor: selectItems.length === 0 ? 'not-allowed' : 'pointer',
              }}
            >
              {selectItems.length > 0 && (
                <span style={{
                  position: 'absolute',
                  top: '-5px',
                  right: '-5px',
                  backgroundColor: 'red',
                  color: 'white',
                  fontSize: '10px',
                  padding: '2px 6px',
                  borderRadius: '50%',
                  zIndex: 1,
                  fontWeight: 'bold'
                }}>
                  {selectItems.length}
                </span>
              )}
              <BiTransferAlt 
                style={{ fontSize: "20px", color: "#32406d" }}
                data-tooltip-id={"remove_search"}
                data-tooltip-content="Profile Assign"
              />
              <ReactTooltip
                style={{ zIndex: 999, padding: "2px", backgroundColor: "#32406d" }}
                place="top-start"
                id="Resume Upload"
              />
            </button>
            {/* Download Resumes */}
            <button
              onClick={handledownloadResumes}
              style={{
                display: 'flex',
                position: 'relative',
                padding: '3px',
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: "5px",
                border: "none",
                height: '30px',
                backgroundColor: '#f1f1f1'
              }}
            >
              {selectItems.length > 0 && (
                <span style={{
                  position: 'absolute',
                  top: '-5px',
                  right: '-5px',
                  backgroundColor: 'red',
                  color: 'white',
                  fontSize: '10px',
                  padding: '2px 6px',
                  borderRadius: '50%',
                  zIndex: 1,
                  fontWeight: 'bold'
                }}>
                  {selectItems.length}
                </span>
              )}
              <FaFileDownload
                style={{ fontSize: "20px", color: "#32406d" }}
                data-tooltip-id={"remove_search"}
                data-tooltip-content="Resumes Download"
              />
              <ReactTooltip
                style={{ zIndex: 999, padding: "2px", backgroundColor: "#32406d" }}
                place="top-start"
                id="Resume Upload"
              />
            </button>

            {/* Send Email */}
            <div
              className="remove_filter_icons"
              onClick={() => {
                if (selectItems.length > 0) {
                  setShowEmailModal(true);
                }
              }}
              style={{
                display: 'flex',
                position: 'relative',
                padding: '3px',
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: "5px",
                height: '30px',
                backgroundColor: '#f1f1f1',
          
                cursor: selectItems.length === 0 ? 'not-allowed' : 'pointer',
              }}
              disabled={selectItems.length === 0}
            >


              <RiMailCheckFill
                style={{ fontSize: "25px", color: "#32406d" }}
                data-tooltip-id="remove_search"
                data-tooltip-content="Send Mail"
              />

              <ReactTooltip
                style={{ zIndex: 999, padding: "2px", backgroundColor: "#32406d" }}
                place="top-start"
                id="Resume Upload"
              />
            </div>

            {/* Global Search Input */}
            {/* <div className="remove_filter_icons" style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
              <VscClearAll
                style={{
                  cursor: 'pointer',
                  height: '24px',
                  width: "24px",
                  color: "#32406d"
                }}
                onClick={() => setGlobalSearchValue('')}
                data-tooltip-id={"clear_search"}
                data-tooltip-content="Clear Search"
              />
              <ReactTooltip
                style={{ zIndex: 999, padding: "2px", backgroundColor: "#32406d" }}
                place="top-start"
                id="clear_search"
              />
            </div> */}
            {/* <div className="search-container">
              <IoMdSearch style={{ display: 'flex', alignItems: 'center', height: "22px", width: "22px", marginRight: "-25px", marginTop: "5px" }} />
              <input
                placeholder="Search across all columns..."
                style={{
                  marginTop: "4px",
                  paddingLeft: "26px",
                  height: "30px",
                  width: "200px",
                  backgroundColor: "rgba(255, 255, 255, 0.80)",
                  border: "none",
                  borderRadius: "5px",
                  padding: "0 25px"
                }}
                className="Search"
                value={globalSearchValue}
                onChange={(e) => {
                  setGlobalSearchValue(e.target.value);
                  setCurrentPage(1); // Reset to first page when searching
                }}
              />
            </div> */}

            {/* Profile Search Input */}
            <div style={{ position: 'relative' }}>
              <IoMdSearch
                style={{ position: 'absolute', top: '4px', left: '8px', height: "22px", width: "22px", color: "#32406d" }}
              />
              <input
                placeholder="Select a profile..."
                className="searching"
                style={{
                  height: "30px",
                  width: "280px",
                  paddingLeft: "35px",
                  borderRadius: "5px",
                  border: "1px solid #ccc"
                }}
                value={searchTerm}
                onChange={handleSearchChange}
                onClick={() => setIsOpen(!isOpen)}
              />
              {isOpen && (
                <ul style={{
                  position: "absolute",
                  top: "31px",
                  width: "490px",
                  maxHeight: "350px",
                  overflowY: "auto",
                  backgroundColor: "#fff",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                  listStyle: "none",
                  padding: "0",
                  margin: "0",
                  zIndex: 1000,
                }}>
                  {filteredProfiles.length > 0 ? (
                    filteredProfiles.map((item, index) => (
                      <li
                        key={index}
                        onClick={() => handleProfileChange(item)}
                        style={{ cursor: "pointer", padding: "4px 10px" }}
                        className="dropdown-item"
                      >
                        {item.name}
                      </li>
                    ))
                  ) : (
                    <li
                      className="dropdown-item no-results"
                      style={{ padding: "8px", color: "#888" }}
                    >
                      No profiles found
                    </li>
                  )}
                </ul>
              )}
            </div>

           
            {/* Skills Input */}
            <input
              placeholder="Enter Skills..."
              style={{
                height: "30px",
                width: "200px",
                backgroundColor: "rgba(255, 255, 255, 0.80)",
                border: "1px solid #ccc",
                borderRadius: "5px",
                padding: "0 10px"
              }}
              value={selectedSkills}
              onChange={(e) => {
                const updatedSkills = e.target.value.split(',').map(skill => skill.trim());
                setSelectedSkills(updatedSkills);
                localStorage.setItem('selectedSkills', JSON.stringify(updatedSkills));
              }}
            />

            {/* Submit Button */}
            <button
              className="remove_filter_icons"
              onClick={handleSubmit}
              style={{
                display: 'flex',
                padding: '1px',
                border: "none",
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: "5px",
                height: "30px",
                backgroundColor: '#f1f1f1',
                cursor: !searchTerm.trim() ? 'not-allowed' : 'pointer'
              }}
              disabled={!searchTerm.trim()}
            >
              {waitForSubmission ? (
                <TailSpin height="25" width="25" color="#4fa94d" />
              ) : (
                <FcCheckmark
                  style={{
                    height: '25px', width: "25px", opacity: !searchTerm.trim() ? 0.5 : 1
                  }}
                  data-tooltip-id="Submit"
                  data-tooltip-content="Submit"
                />
              )}
              <ReactTooltip
                style={{ zIndex: 999, padding: "4px", backgroundColor: "#32406d" }}
                place="top-start"
                id="Submit"
              />
            </button>

            {/* Clear Search */}
            <div
              className="remove_filter_icons"
              onClick={() => {
                setSearchTerm('');
                setSelectedSkills([]);
                setSelectedclient('')
                setShowAllCandidates(true);
              }}
              style={{
                display: 'flex',
                padding: '3px',
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: "5px",
                height: '30px',
                backgroundColor: '#f1f1f1',
                cursor: 'pointer'
              }}
            >
              <MdOutlineYoutubeSearchedFor
                style={{ height: '25px', width: "25px", color: "#32406d" }}
                data-tooltip-id="remove_search"
                data-tooltip-content="Clear search"
              />
              <ReactTooltip
                style={{ zIndex: 999, padding: "2px", backgroundColor: "#32406d" }}
                place="top-start"
                id="remove_search"
              />
            </div>
          </div>


        </div>


        <div
          className="dashr"

        >

        </div>

        <div
          className="dashcontainer"
       
          style={{
            position: "relative",
            width: "100%",
            padding: "5px 5px",
            background: "rgba(255, 255, 255, 0.25)",
            boxShadow: "0 8px 32px 0 rgba(31, 38, 135, 0.37)",
            backdropFilter: "blur(11.5px)",
            borderRadius: "10px",
            border: "1px solid rgba(255, 255, 255, 0.18)",
            height: "100%",
            overflow: "hidden",
          }}>

          <div className="table-container" style={{
            //height: "520px",
            overflow: "auto",
            marginTop: "5px"
          }}>


            <table className="max-width-fit-content table" style={{ width: "100%", tableLayout: "fixed", marginTop: "0" }} id="candidates-table">
              <thead>
                <tr>
                  <th style={{width:"95px"}}> <span>Select all </span>

                    <input
                      style={{ cursor: "pointer" }}
                      id="all"
                      type="checkbox"
                      onChange={handleSelectAll}
                      checked={
                        paginatedData.length > 0 &&
                        paginatedData.every((item) =>
                          selectItems.some((selected) => selected.id === item.id)
                        )
                      }
                      data-tooltip-id={"select_all"}
                      data-tooltip-content="Select All"

                    />
                    <ReactTooltip
                      style={{ zIndex: 999, padding: "4px", backgroundColor: "#32406D", }}
                      place="bottom"
                      id="select_all"
                    />
                  </th>
                  <th style={{ color: showSearchjobassignment.showSearchdate ? "orange" : "white", fontSize: "13px" }} >
                     <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}></div>
                    <span
                      id={"date_label_ref"}
                      onClick={() => {

                        setshowSearchjobassignment((prev) => ({
                          ...Object.fromEntries(
                            Object.keys(prev).map((key) => [
                              key,
                              key === "showSearchdate"
                                ? !prev.showSearchdate
                                : false,
                            ]),
                          ),
                        }));
                      }}
                      style={{ cursor: "pointer" }}  >Applied Date</span>
                    <div style={{ display: 'flex', alignItems: 'right' }}>
                      <span
                        onClick={() => handleSort('date_created')}
                        style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                      >
                        {getSortIcon('date_created')}
                      </span>
                      <MdFilterAlt
                        style={{ color: isDateFiltered ? "orange" : "white" }}
                        id={"date_ref"}
                        className="arrow"
                        onClick={() => {

                          setshowSearchjobassignment((prev) => ({
                            ...Object.fromEntries(
                              Object.keys(prev).map((key) => [
                                key,
                                key === "showSearchdate"
                                  ? !prev.showSearchdate
                                  : false,
                              ]),
                            ),
                          }));
                        }}
                      />
                    </div>
                    </div>
                    {showSearchjobassignment.showSearchdate && (
                      <div
                        ref={uniRef}
                        className="Filter-popup"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <form
                          id="filter-form"
                          className="Filter-inputs-container"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <ul>
                            <li>
                              <input
                                type="checkbox"
                                style={{
                                  width: "12px",
                                  marginRight: "5px",
                                }}
                                checked={selectAllDate}
                                onChange={(e) => handleSelectAllForDate(e)}
                                onClick={(e) => e.stopPropagation()}
                              />
                              <label
                                style={{
                                  marginBottom: "0px",
                                  fontWeight: "400",
                                  fontSize: '13px',
                                  cursor: 'pointer',
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleSelectAllForDate(e);
                                }}>
                                Select all
                              </label>
                            </li>
                                     <li>

                              {[
                                { label: "Last 7 Days", days: 7 },
                                { label: "Last 15 Days", days: 15 },
                                { label: "Last 30 Days", days: 30 },
                              ].map(({ label, days }) => {
                                const isChecked = dateRangeSelected.includes(label);
                                return (
                                  <div key={label} className="filter-inputs">
                                    <input
                                      type="checkbox"
                                      style={{ width: "12px" }}
                                      checked={isChecked}
                                      onChange={() => handleDateRangeCheckbox(label, days)}
                                    />
                                    <label
                                      style={{ marginBottom: "0px", fontWeight: "400", cursor: "pointer" }}
                                      onClick={() => handleDateRangeCheckbox(label, days)}
                                    >
                                      {label}
                                    </label>
                                  </div>
                                );
                              })}
                            </li>
                            <li style={{
                              marginBottom:"5px"
                            }}>
                              <span style={{
                                border: "1px solid",
                                background: "#32406d",
                                color: "white",
                                cursor: "pointer",
                                padding: "3px",
                                borderRadius: "5px",
                                marginBottom: "20px"

                              }} onClick={() => setShowCustomDatePopup((prev) => !prev)}>Date filter by Range</span>
                            </li>
                            <li>
                              {uniqueDataDate
                               
                                .map((date_created, index) => (
                                  <div key={index} className="filter-inputs">
                                    <input
                                      type="checkbox"
                                      style={{
                                        width: "12px",
                                      }}
                                      checked={dateSelected.includes(
                                        date_created,
                                      )}
                                      onChange={(e) =>
                                        handleCheckboxChangeForDate(
                                          date_created,
                                          e
                                        )
                                      }
                                      onClick={(e) => e.stopPropagation()}
                                    />
                                    <label
                                      style={{
                                        marginBottom: "0px",
                                        fontWeight: "400",
                                        cursor: 'pointer',
                                      }}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleCheckboxChangeForDate(
                                          date_created,
                                          e
                                        );
                                      }}
                                    >
                                      {/* {date_created} */}
                                   {new Date(date_created).toLocaleDateString("en-GB").replaceAll('/', '-')}
                                    </label>
                                  </div>
                                ))}
                            </li>
                          </ul>
                        </form>
                        
                      </div>
                    )}
                       {showCustomDatePopup && (
                      <div
                        style={{
                          position: "absolute",
                          backgroundColor: "white",
                          color: "black",
                          padding: "10px",
                          paddingTop: "30px", // Added space for the close button
                          borderRadius: "6px",
                          boxShadow: "0 4px 12px rgba(0,0,0,0.15)", // A slightly softer shadow
                          zIndex: 9999,
                          top: "100%",
                            marginLeft: "60%",
                          width: "180px", // Set a fixed width for better layout
                        }}
                      >
                        {/* -- Close Button -- */}
                        <button
                          onClick={() => setShowCustomDatePopup(false)}
                          aria-label="Close" // Important for accessibility
                          style={{
                            position: "absolute",
                            top: "8px",
                            right: "8px",
                            background: "none",
                            border: "none",
                            cursor: "pointer",
                            fontSize: "18px",
                            color: "#888",
                            padding: "5px",
                            lineHeight: "1",
                          }}
                        >
                          &times; {/* This is the 'X' symbol */}
                        </button>

                        <div style={{ marginBottom: "10px" }}> {/* Increased margin */}
                          <label style={{ fontSize: "12px", display: "block", marginBottom: "4px" }}>
                            From:
                          </label>
                          <input
                            type="date"
                            value={fromDate}
                            onChange={(e) => setFromDate(e.target.value)}
                            style={{ fontSize: "12px", padding: "6px", width: "100%", boxSizing: "border-box" }}
                          />
                        </div>
                        <div style={{ marginBottom: "10px" }}>
                          <label style={{ fontSize: "12px", display: "block", marginBottom: "4px" }}>
                            To:
                          </label>
                          <input
                            type="date"
                            value={toDate}
                            onChange={(e) => setToDate(e.target.value)}
                            style={{ fontSize: "12px", padding: "6px", width: "100%", boxSizing: "border-box" }}
                          />
                        </div>

                        {/* -- Apply Button -- */}
                        <button
                          onClick={() => {
                            handleCustomDateFilter();
                            setShowCustomDatePopup(false);
                          }}
                          style={{
                            fontSize: "12px",
                            padding: "8px 12px", // Increased padding
                            marginTop: "5px",
                            cursor: "pointer",
                            backgroundColor: "#1976d2",
                            color: "white",
                            border: "none",
                            borderRadius: "4px",
                            width: "100%", // Make button full-width
                          }}
                        >
                          Apply
                        </button>
                      </div>
                    )}
                  </th>

                  <th style={{  color: showSearchjobassignment.showSearchName ? "orange" : "white", fontSize: "13px" }}>
                     <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <span
                      style={{ cursor: "pointer" }}
                      id={"name_label_ref"}
                      onClick={() => {
                        setshowSearchjobassignment((prev) => ({
                          ...Object.fromEntries(
                            Object.keys(prev).map((key) => [
                              key,
                              key === "showSearchName"
                                ? !prev.showSearchName
                                : false,
                            ]),
                          ),
                        }));
                      }}
                    >  Name  {" "}</span>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <span
                        onClick={() => handleSort('name')}
                        style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                      >
                        {getSortIcon('name')}
                      </span>
                      <MdFilterAlt
                        style={{ color: isnameFiltered ? "orange" : "white" }}
                        id={"name_ref"}
                        className="arrow"
                        onClick={() => {
                          setshowSearchjobassignment((prev) => ({
                            ...Object.fromEntries(
                              Object.keys(prev).map((key) => [
                                key,
                                key === "showSearchName"
                                  ? !prev.showSearchName
                                  : false,
                              ]),
                            ),
                          }));
                        }}
                      />
                    </div>
                    </div>
                    {showSearchjobassignment.showSearchName && (
                      <div
                        ref={uniRef}
                        className="Filter-popup"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <form
                          id="filter-form"
                          className="Filter-inputs-container"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <ul>
                            <li>
                              <input
                                type="checkbox"
                                style={{
                                  width: "12px",
                                  marginRight: "5px",
                                }}
                                checked={selectAll}
                                onChange={(e) => handleSelectAllForName(e)}
                                onClick={(e) => e.stopPropagation()}
                              />
                              <label
                                style={{
                                  marginBottom: "0px",
                                  fontWeight: "400",
                                  fontSize: '13px',
                                  cursor: 'pointer',
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleSelectAllForName(e);
                                }}>
                                Select all
                              </label>
                            </li>
                            <li>
                              {uniqueDataNames
                             
                                 .sort((a, b) =>
                                  a.replace(/\s+/g, '').toLowerCase().localeCompare(
                                    b.replace(/\s+/g, '').toLowerCase()
                                  )
                                )
                                .map((name, index) => (
                                  <div
                                    key={index}
                                    className="filter-inputs"
                                  >
                                    <input
                                      type="checkbox"
                                      style={{
                                        width: "12px",
                                      }}
                                      checked={nameSelected.includes(
                                        name.toLowerCase(),
                                      )}
                                      onChange={(e) =>
                                        handleCheckboxChange(
                                          name.toLowerCase(),
                                          e
                                        )
                                      }
                                      onClick={(e) => e.stopPropagation()}
                                    />
                                    <label
                                      style={{
                                        marginBottom: "0px",
                                        fontWeight: "400",
                                        cursor: 'pointer',
                                      }}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleCheckboxChange(
                                          name.toLowerCase(),
                                          e
                                        );
                                      }}
                                    >
                                      {name}
                                    </label>
                                  </div>
                                ))}
                            </li>
                          </ul>
                        </form>

                      </div>
                    )}
                  </th>
                  <th style={{ color: showSearchjobassignment.showSearchEmail ? "orange" : "white", fontSize: "13px" }}>
                     <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <span
                      style={{ cursor: "pointer" }}
                      id={"email_label_ref"}
                      onClick={() => {
                        setshowSearchjobassignment((prev) => ({
                          ...Object.fromEntries(
                            Object.keys(prev).map((key) => [
                              key,
                              key === "showSearchEmail"
                                ? !prev.showSearchEmail
                                : false,
                            ]),
                          ),
                        }));
                      }}
                    >Email{" "}</span>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <span
                        onClick={() => handleSort('email')}
                        style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                      >
                        {getSortIcon('email')}
                      </span>
                      <MdFilterAlt
                        style={{ color: isemailFiltered ? "orange" : "white" }}
                        id={"email_ref"}
                        className="arrow"
                        onClick={() => {
                          setshowSearchjobassignment((prev) => ({
                            ...Object.fromEntries(
                              Object.keys(prev).map((key) => [
                                key,
                                key === "showSearchEmail"
                                  ? !prev.showSearchEmail
                                  : false,
                              ]),
                            ),
                          }));
                        }}
                      />
                    </div>
                    </div>
                    {showSearchjobassignment.showSearchEmail && (
                      <div ref={uniRef} className="Filter-popup">
                        <form
                          id="filter-form-email"
                          className="Filter-inputs-container"
                        >
                          <ul>
                            <li>
                              <input
                                type="checkbox"
                                style={{
                                  width: "12px",
                                  marginRight: "5px",
                                }}
                                checked={selectAllEmail}
                                onChange={(e) => {
                                  const allChecked = !selectAllEmail;
                                  setSelectAllEmail(allChecked);
                                  if (allChecked) {
                                    setEmailSelected(uniqueDataEmail.map((d) => d?.toLowerCase()));
                                  } else {
                                    setEmailSelected([]);
                                  }
                                }}
                              />
                              <label
                                style={{
                                  marginBottom: "0px",
                                  fontWeight: "400",
                                  cursor: 'pointer',
                                  fontSize: '13px',
                                }}
                                onClick={() => {
                                  const allChecked = !selectAllEmail;
                                  setSelectAllEmail(allChecked);
                                  if (allChecked) {
                                    setEmailSelected(uniqueDataEmail.map((d) => d?.toLowerCase()));
                                  } else {
                                    setEmailSelected([]);
                                  }
                                }}
                              >
                                Select all
                              </label>
                            </li>
                            <li>
                              {uniqueDataEmail
                                .sort((a, b) =>
                                  a.replace(/\s+/g, '').toLowerCase().localeCompare(
                                    b.replace(/\s+/g, '').toLowerCase()
                                  )
                                )
                                .map((email, index) => (
                                  <div
                                    key={index}
                                    className="filter-inputs"
                                  >
                                    <input
                                      type="checkbox"
                                      style={{
                                        width: "12px",
                                      }}
                                      checked={emailSelected.includes(
                                        email.toLowerCase(),
                                      )}
                                      onChange={(e) => {
                                        const isSelected = emailSelected.includes(email.toLowerCase());
                                        if (isSelected) {
                                          setEmailSelected((prevSelected) =>
                                            prevSelected.filter((item) => item !== email.toLowerCase()),
                                          );
                                          setSelectAllEmail(false);
                                        } else {
                                          setEmailSelected((prevSelected) => [...prevSelected, email.toLowerCase()]);
                                          setSelectAllEmail(emailSelected.length === uniqueDataEmail.length - 1);
                                        }
                                      }}
                                    />
                                    <label
                                      style={{
                                        marginBottom: "0px",
                                        fontWeight: "400",
                                        cursor: 'pointer',
                                      }}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        const isSelected = emailSelected.includes(email.toLowerCase());
                                        if (isSelected) {
                                          setEmailSelected((prevSelected) =>
                                            prevSelected.filter((item) => item !== email.toLowerCase()),
                                          );
                                          setSelectAllEmail(false);
                                        } else {
                                          setEmailSelected((prevSelected) => [...prevSelected, email.toLowerCase()]);
                                          setSelectAllEmail(emailSelected.length === uniqueDataEmail.length - 1);
                                        }
                                      }}
                                    >
                                      {email}
                                    </label>
                                  </div>
                                ))}
                            </li>
                          </ul>
                        </form>
                      </div>
                    )}
                  </th>
                  <th style={{  color: showSearchjobassignment.showSearchMobile ? "orange" : "white", fontSize: "13px" }}>
                     <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <span
                      style={{ cursor: "pointer" }}
                      id={"mobile_label_ref"}
                      onClick={() => {
                        setshowSearchjobassignment((prev) => ({
                          ...Object.fromEntries(
                            Object.keys(prev).map((key) => [
                              key,
                              key === "showSearchMobile"
                                ? !prev.showSearchMobile
                                : false,
                            ]),
                          ),
                        }));
                      }}
                    >Mobile{" "}</span>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <span
                        onClick={() => handleSort('mobile')}
                        style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                      >
                        {getSortIcon('mobile')}
                      </span>
                      <MdFilterAlt
                        style={{ color: ismobileFiltered ? "orange" : "white" }}
                        id={"mobile_ref"}
                        className="arrow"
                        onClick={() => {
                          setshowSearchjobassignment((prev) => ({
                            ...Object.fromEntries(
                              Object.keys(prev).map((key) => [
                                key,
                                key === "showSearchMobile"
                                  ? !prev.showSearchMobile
                                  : false,
                              ]),
                            ),
                          }));
                        }}
                      />
                    </div>
                    </div>
                    {showSearchjobassignment.showSearchMobile && (
                      <div ref={uniRef} className="Filter-popup">
                        <form
                          id="filter-form-mobile"
                          className="Filter-inputs-container"
                        >
                          <ul>
                            <li>
                              <input
                                type="checkbox"
                                style={{
                                  width: "12px",
                                  marginRight: "5px",
                                }}
                                checked={selectAllMobile}
                                onChange={(e) => {
                                  const allChecked = !selectAllMobile;
                                  setSelectAllMobile(allChecked);
                                  if (allChecked) {
                                    setMobileSelected(uniqueDataMobile.map((d) => d.toString()));
                                  } else {
                                    setMobileSelected([]);
                                  }
                                }}
                              />
                              <label
                                style={{
                                  marginBottom: "0px",
                                  fontWeight: "400",
                                  cursor: 'pointer',
                                  fontSize: '13px',
                                }}
                                onClick={() => {
                                  const allChecked = !selectAllMobile;
                                  setSelectAllMobile(allChecked);
                                  if (allChecked) {
                                    setMobileSelected(uniqueDataMobile.map((d) => d.toString()));
                                  } else {
                                    setMobileSelected([]);
                                  }
                                }}
                              >
                                Select all
                              </label>
                            </li>
                            <li>
                              {uniqueDataMobile
                                .sort((a, b) => a.toString().localeCompare(b.toString()))
                                .map((mobile, index) => (
                                  <div
                                    key={index}
                                    className="filter-inputs"
                                  >
                                    <input
                                      type="checkbox"
                                      style={{
                                        width: "12px",
                                      }}
                                      checked={mobileSelected.includes(
                                        mobile.toString(),
                                      )}
                                      onChange={(e) => {
                                        const isSelected = mobileSelected.includes(mobile.toString());
                                        if (isSelected) {
                                          setMobileSelected((prevSelected) =>
                                            prevSelected.filter((item) => item !== mobile.toString()),
                                          );
                                          setSelectAllMobile(false);
                                        } else {
                                          setMobileSelected((prevSelected) => [...prevSelected, mobile.toString()]);
                                          setSelectAllMobile(mobileSelected.length === uniqueDataMobile.length - 1);
                                        }
                                      }}
                                    />
                                    <label
                                      style={{
                                        marginBottom: "0px",
                                        fontWeight: "400",
                                        cursor: 'pointer',
                                      }}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        const isSelected = mobileSelected.includes(mobile.toString());
                                        if (isSelected) {
                                          setMobileSelected((prevSelected) =>
                                            prevSelected.filter((item) => item !== mobile.toString()),
                                          );
                                          setSelectAllMobile(false);
                                        } else {
                                          setMobileSelected((prevSelected) => [...prevSelected, mobile.toString()]);
                                          setSelectAllMobile(mobileSelected.length === uniqueDataMobile.length - 1);
                                        }
                                      }}
                                    >
                                      {mobile}
                                    </label>
                                  </div>
                                ))}
                            </li>
                          </ul>
                        </form>
                      </div>
                    )}
                  </th>
                  <th style={{  color: showSearchjobassignment.showSearchClient ? "orange" : "white", fontSize: "13px" }}>
                     <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <span
                      style={{ cursor: "pointer" }}
                      id={"client_label_ref"}
                      onClick={() => {
                        setshowSearchjobassignment((prev) => ({
                          ...Object.fromEntries(
                            Object.keys(prev).map((key) => [
                              key,
                              key === "showSearchClient"
                                ? !prev.showSearchClient
                                : false,
                            ]),
                          ),
                        }));
                      }}
                    >Client{" "}</span>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <span
                        onClick={() => handleSort('client')}
                        style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                      >
                        {getSortIcon('client')}
                      </span>
                      <MdFilterAlt
                        style={{
                          color: isclientFiltered ? "orange" : "white",
                        }}
                        id={"client_ref"}
                        className="arrow"
                        onClick={() => {
                          setshowSearchjobassignment((prev) => ({
                            ...Object.fromEntries(
                              Object.keys(prev).map((key) => [
                                key,
                                key === "showSearchClient"
                                  ? !prev.showSearchClient
                                  : false,
                              ]),
                            ),
                          }));
                        }}
                      />
                    </div>
                    </div>
                    {showSearchjobassignment.showSearchClient && (
                      <div ref={uniRef} className="Filter-popup">
                        <form
                          id="filter-form-client"
                          className="Filter-inputs-container"
                        >
                          <ul>
                            <li>
                              <input
                                type="checkbox"
                                style={{
                                  width: "12px",
                                  marginRight: "5px",
                                }}
                                checked={selectAllClient}
                                onChange={handleSelectAllForClient}
                              />
                              <label
                                style={{
                                  marginBottom: "0px",
                                  fontWeight: "400",
                                  cursor: 'pointer',
                                  fontSize: '13px',
                                }}
                                onClick={() => handleSelectAllForClient()}
                              >
                                Select all
                              </label>

                            </li>
                            <li>
                              {uniqueDataClient
                               
                                 .sort((a, b) =>
                                  a.replace(/\s+/g, '').toLowerCase().localeCompare(
                                    b.replace(/\s+/g, '').toLowerCase()
                                  )
                                )
                                .map((client, index) => (
                                  <div
                                    key={index}
                                    className="filter-inputs"
                                  >
                                    <input
                                      type="checkbox"
                                      style={{
                                        width: "12px",
                                      }}
                                      checked={clientSelected.includes(
                                        client.toLowerCase(),
                                      )}
                                      onChange={(e) =>{
                                        handleCheckboxChangeClient(
                                          client.toLowerCase(),
                                          e
                                        )
                                      }}
                                    />
                                    <label
                                      style={{
                                        marginBottom: "0px",
                                        fontWeight: "400",
                                        cursor: 'pointer',
                                      }}
                                      onClick={(e) => {
                                        e.stopPropagation()
                                        ;handleCheckboxChangeClient(
                                          client.toLowerCase(),
                                          e
                                        )
                                      }}
                                    >
                                      {client}
                                    </label>
                                  </div>
                                ))}
                            </li>
                          </ul>
                        </form>
                       
                      </div>
                    )}
                  </th>
                   

                  <th style={{  color: showSearchjobassignment.showSearchSkill ? "orange" : "white", fontSize: "13px" }}>
                     <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <span
                      style={{ cursor: "pointer" }}
                      id={"skills_label_ref"}
                      onClick={() => {
                        setshowSearchjobassignment((prev) => ({
                          ...Object.fromEntries(
                            Object.keys(prev).map((key) => [
                              key,
                              key === "showSearchSkill"
                                ? !prev.showSearchSkill
                                : false,
                            ]),
                          ),
                        }));
                      }}
                    >Skills{" "}</span>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <span
                        onClick={() => handleSort('skills')}
                        style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                      >
                        {getSortIcon('skills')}
                      </span>
                      <MdFilterAlt
                        style={{
                          color: isskillFiltered ? "orange" : "white",
                        }}
                        id={"skills_ref"}
                        className="arrow"
                        onClick={() => {
                          setshowSearchjobassignment((prev) => ({
                            ...Object.fromEntries(
                              Object.keys(prev).map((key) => [
                                key,
                                key === "showSearchSkill"
                                  ? !prev.showSearchSkill
                                  : false,
                              ]),
                            ),
                          }));
                        }}
                      />
                    </div>
                    </div>
                    {showSearchjobassignment.showSearchSkill && (
                      <div
                        ref={uniRef}
                        className="Filter-popup"
                        style={{ width: "300px" ,marginLeft:"20px"}}
                      >
                        <form
                          id="filter-form"
                          className="Filter-inputs-container"
                        >
                          <ul>
                            <li>
                              <input
                                type="checkbox"
                                style={{
                                  width: "12px",
                                  marginRight: "5px",
                                }}
                                checked={selectAllSkill}
                                onChange={handleSelectAllForSkill}
                              />
                              <label
                                style={{
                                  marginBottom: "0px",
                                  fontWeight: "400",
                                  cursor: 'pointer',
                                  fontSize: '13px',
                                }}
                                onClick={() => handleSelectAllForSkill()} >
                                Select all
                              </label>
                            </li>
                            <li>
                              {uniqueDataSkill
                             
                                 .sort((a, b) =>
                                  a.replace(/\s+/g, '').toLowerCase().localeCompare(
                                    b.replace(/\s+/g, '').toLowerCase()
                                  )
                                )
                                .map((skills, index) => (
                                  <div
                                    key={index}
                                    className="filter-inputs"
                                  >
                                    <input
                                      type="checkbox"
                                      style={{
                                        width: "12px",
                                      }}
                                      checked={skillSelected.includes(
                                        skills.toLowerCase(),
                                      )}
                                      onChange={(e) =>{
                                        handleCheckboxChangeSkill(
                                          skills.toLowerCase(),
                                          e
                                        )
                                      }}
                                    />
                                    <label
                                      style={{
                                        marginBottom: "0px",
                                        fontWeight: "400",
                                        cursor: 'pointer',
                                      }}
                                      onClick={(e) =>{
                                        e.stopPropagation();
                                        handleCheckboxChangeSkill(
                                        skills.toLowerCase(),
                                        e
                                      )}}
                                    >
                                      {skills}
                                    </label>
                                  </div>
                                ))}
                            </li>
                          </ul>
                        </form>
                       
                      </div>
                    )}
                  </th>

                  <th style={{  color: showSearchjobassignment.showSearchStatus ? "orange" : "white", fontSize: "13px" }}>
                   <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <span
                      style={{ cursor: "pointer" }}
                      id={"status_label_ref"}
                      onClick={() => {
                        setshowSearchjobassignment((prev) => ({
                          ...Object.fromEntries(
                            Object.keys(prev).map((key) => [
                              key,
                              key === "showSearchStatus"
                                ? !prev.showSearchStatus
                                : false,
                            ]),
                          ),
                        }));
                      }}
                    >Status{" "}</span>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <span
                        onClick={() => handleSort('status')}
                        style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                      >
                        {getSortIcon('status')}
                      </span>
                      <MdFilterAlt
                        style={{
                          color: isstatusFiltered || isColorFiltered ? "orange" : "white",
                        }}
                        id={"status_ref"}
                        className="arrow"
                        onClick={() => {
                          setshowSearchjobassignment((prev) => ({
                            ...Object.fromEntries(
                              Object.keys(prev).map((key) => [
                                key,
                                key === "showSearchStatus"
                                  ? !prev.showSearchStatus
                                  : false,
                              ]),
                            ),
                          }));
                        }}
                      />
                    </div>
                  </div>

                    {showSearchjobassignment.showSearchStatus && (
                      <div ref={uniRef} className="Filter-popup" style={{marginLeft:"20px"}}>
                        <form
                          id="filter-form-client"
                          className="Filter-inputs-container"
                        >
               
                          <ul>
                            <li>
                              <input
                                type="checkbox"
                                style={{
                                  width: "12px",
                                  marginRight: "5px",
                                }}
                                checked={selectAllStatus}
                                onChange={handleSelectAllForStatus}
                              />
                              <label
                                style={{
                                  marginBottom: "0px",
                                  fontWeight: "400",
                                  cursor: 'pointer',
                                  fontSize: '13px',
                                }}
                                onClick={() => handleSelectAllForStatus()} >
                                Select all
                              </label>
                            </li>
                            <li>
                              {uniqueDataStatus
                             
                                 .sort((a, b) =>
                                  a.replace(/\s+/g, '').toLowerCase().localeCompare(
                                    b.replace(/\s+/g, '').toLowerCase()
                                  )
                                )
                                .map((status, index) => (
                                  <div
                                    key={index}
                                    className="filter-inputs"
                                  >
                                    <input
                                      type="checkbox"
                                      style={{
                                        width: "12px",
                                      }}
                                      checked={statusSelected.includes(
                                        status.toLowerCase(),
                                      )}
                                      onChange={(e) =>{
                                        handleCheckboxChangeStatus(
                                          status.toLowerCase(),
                                          e
                                        )
                                      }}
                                    />
                                    <label
                                      style={{
                                        marginBottom: "0px",
                                        fontWeight: "400",
                                        cursor: 'pointer',
                                      }}
                                      onClick={(e) => {

                                        e.stopPropagation();
                                        handleCheckboxChangeStatus(
                                        status.toLowerCase(),
                                        e
                                      )}}
                                    >
                                      {status}
                                    </label>
                                  </div>
                                ))}
                            </li>
                          </ul>
                        </form>
                       
                      </div>
                    )}
                  </th>
                  <th style={{width:"80px"}}>Resume</th>
                    <th style={{ width: "180px", color: showSearchjobassignment.showSearchmatchpercent ? "orange" : "white", }}>
                                          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                            <span
                                              id={"match_percent_label_ref"}
                                              style={{ cursor: "pointer" }}
                                              onClick={() => {
                                                setshowSearchjobassignment((prev) => ({
                                                  ...Object.fromEntries(
                                                    Object.keys(prev).map((key) => [
                                                      key,
                                                      key === "showSearchmatchpercent"
                                                        ? !prev.showSearchmatchpercent
                                                        : false,
                                                    ]),
                                                  ),
                                                }));
                                              }}
                                            >Match Percentage{" "}</span>
                                            <div style={{ display: 'flex', alignItems: 'center' }}>
                                              <span
                                                onClick={() => handleSort('match_score')}
                                                style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                                              >
                                                {getSortIcon('match_score')}
                                              </span>
                                              <MdFilterAlt
                                                style={{
                                                  color: ismatchpercentFiltered ? "orange" : "white",
                                                  fontSize: '16px'

                                                }}
                                                id={"match_percent_ref"}
                                                className="arrow"
                                                onClick={() => {
                                                  setshowSearchjobassignment((prev) => ({
                                                    ...Object.fromEntries(
                                                      Object.keys(prev).map((key) => [
                                                        key,
                                                        key === "showSearchmatchpercent"
                                                          ? !prev.showSearchmatchpercent
                                                          : false,
                                                      ]),
                                                    ),
                                                  }));
                                                }}
                                              />
                                            </div>
                                          </div>
                                          {showSearchjobassignment.showSearchmatchpercent && (
                                            <div ref={uniRef} className="Filter-popup"  style={{marginLeft:"0px"}}>
                                              <form
                                                id="filter-form-user"
                                                className="Filter-inputs-container"
                                              >
                                                <ul>
                                                  <li>
                                                    <input
                                                      type="checkbox"
                                                      style={{
                                                        width: "12px",
                                                        marginRight: "5px",
                                                      }}
                                                      checked={selectAllMatchpercent}
                                                      onChange={handleSelectAllMatchpercent}
                                                    />
                                                    <label
                                                      style={{
                                                        marginBottom: "0px",
                                                        fontWeight: "400",
                                                        fontSize: '13px',
                                                        cursor: 'pointer',
                                                      }}
                                                      onClick={() => handleSelectAllMatchpercent()}>
                                                      Select all
                                                    </label>
                                                  </li>
                                                  {/* <li className="filtersearch">
                                                    <input
                                                      type="text"
                                                      placeholder="Search JobId..."
                                                      value={searchJobId}
                                                      onChange={(e) => setSearchMatchpercent(e.target.value)}
                                                      style={{
                                                        // width: '95%',
                                                        padding: '4px',
                                                        fontSize: '13px',
                                                        // marginBottom: '8px',
                                                        border: '1px solid #ccc',
                                                        borderRadius: '4px'
                                                      }}
                                                    />
                                                  </li> */}
                                                  <li>
  {matchPercentRanges.map((range, index) => (
    <div key={index} className="filter-inputs">
      <input
        type="checkbox"
        style={{ width: "12px" }}
        checked={matchpercentSelected.includes(range.label)}
        onChange={() => handleCheckboxMatchpercent(range.label)}
      />
      <label
        style={{ marginBottom: "0px", cursor: 'pointer' }}
        onClick={() => handleCheckboxMatchpercent(range.label)}
      >
        {range.label}
      </label>
    </div>
  ))}
</li>

                                                </ul>
                                              </form>
                
                                            </div>
                                          )}
                                        </th>
                </tr>
              </thead>
              {!hasSubmitted ? (
                <tbody>
                  <tr>
                    <td colSpan="9" style={{ textAlign: "center", fontSize: "16px", padding: "10px", color: "grey" }}>
                      Please submit profile  to show Data
                    </td>
                  </tr>
                </tbody>
              ) : loading ? (
                <tbody>
                  <tr>
                    <td colSpan="9" style={{ textAlign: "center", fontSize: "16px", padding: "10px", color: "grey" }}>

                      <Hourglass
                        height="100"
                        width="60"
                        ariaLabel="hourglass-loading"
                        wrapperStyle={{}}
                        wrapperClass=""
                        colors={["#306cce", "#72a1ed"]}
                        style={{ zIndex: "999" }}
                      />
                      <p className="loader-text" color="green">Loading...</p>

                    </td>
                  </tr>
                </tbody>
              ) : (
                <tbody className="scrollable-body resume">
                  {sortedAndFilteredData?.length === 0 ? (
                    <tr>
                      <td colSpan="9" style={{ textAlign: "center", fontSize: "16px", padding: "10px", color: "red" }}>
                        No candidates found for this role.
                      </td>
                    </tr>
                  ) : (
                    paginatedData.map((candidate, idx) => (
                      <tr key={candidate.candidate_id || idx}>

                        <td>
                          <input
                            type="checkbox"
                            style={{ cursor: "pointer" }}
                            checked={selectItems.some(selectItem => selectItem.id === candidate.id)}
                            onChange={(e) => handleSelectIndividual(candidate.id, e)}
                          />

                        </td>
                        <td>
                        {new Date(candidate.date_created).toLocaleDateString("en-GB").replaceAll('/', '-')}
                          </td>

                        <td style={{
                          textAlign: "left",
                          padding: "5px",
                          borderBottom: "1px solid #ddd",
                          whiteSpace: "normal",
                          wordWrap: "break-word",
                        }}>{candidate.name}</td>

                        <td
                          name="email_td"
                          id={showItems[idx]?.id + "2"}
                          style={{
                            padding: "5px",
                            borderBottom: "1px solid #ddd",
                            textAlign: "left",
                            // whiteSpace: "normal",
                            // wordWrap: "break-word",
                          }}>

                          {candidate.email}
                          {showItems && showItems[idx] && showItems[idx].email ? (
                            <div
                              id={"default2"}
                              style={{
                                position: "absolute",
                                Width: "auto",
                                maxWidth: "350px",
                                height: "auto",
                                backgroundColor: "rgba(255, 255, 255, 0.8)",
                                border: "1px solid #666",
                                borderRadius: "10px",
                                padding: "10px",
                                boxShadow: "0px 0px 10px rgba(0,0,0,0.2)",
                                zIndex: "9999", // Ensure the div appears above other content
                                wordWrap: "break-word",
                                whiteSpace: "normal",
                              }}
                            >
                              {candidate.email}
                            </div>
                          ) : (
                            ""
                          )}
                        </td>
                        <td>
                          {candidate.mobile}
                        </td>
                        <td
                          style={{
                            textAlign: "left"
                          }}
                        >{candidate.client}</td>
                    
                        <td id={showItems[idx]?.id + "1"}
                          style={{
                            // position:'relative',
                            textAlign: "left",
                            padding: "5px",
                            borderBottom: "1px solid #ddd",
                          }}>
                          {candidate.skills || "No skills available"}
                          {showItems && showItems[idx] && showItems[idx].skills ? (
                            <div
                              id={"default1"}
                              style={{
                                position: "absolute",
                                Width: "auto",
                                maxWidth: "350px",
                                height: "auto",
                                backgroundColor: "rgba(255, 255, 255, 0.8)",
                                border: "1px solid #666",
                                borderRadius: "10px",
                                padding: "10px",
                                boxShadow: "0px 0px 10px rgba(0,0,0,0.2)",
                                zIndex: "9999", // Ensure the div appears above other content
                                wordWrap: "break-word",
                                whiteSpace: "normal",
                              }}
                            >
                              {candidate.skills || "No skills available"}
                            </div>
                          ) : null}


                        </td>
                        <td style={{
                          textAlign: "left",
                          padding: "5px",
                          borderBottom: "1px solid #ddd",
                          whiteSpace: "normal",
                          wordWrap: "break-word",
                          color: getStatusColor(candidate.status),
                        }}
                        >{candidate.status}</td>
                        <td style={{ width: "60px" }}>
                          <FontAwesomeIcon
                           
                            icon={faFileAlt}
                          
                            style={{
                              color: candidate.resume_base64 ? "green" : "gray",
                              fontSize: "18px",
                              cursor: "pointer"
                            }}
                            onClick={() => {

                              resumeApiCall(candidate);

                            }}
                          />
                          
                        </td>
                         <td
 
>
  <span  style={{
      color:
      candidate.match_score * 100 >= 85
        ? "green"
        : candidate.match_score * 100 >= 60
        ? "orange"
        : candidate.match_score * 100 >= 50
        ? "red"
        : "transparent",
    // color:
    //   candidate.match_score * 100 >= 50 ? "#fff" : "inherit",
    //   padding:"10px",
    //   margin:"5px",
    //   borderRadius:"10px"
  }}> {(candidate.match_score * 100).toFixed(2)}%</span>
</td>
                      </tr>
                    ))
                  )}
                </tbody>
              )}
            </table>


          </div>
        </div>


        {showAllCandidates ? (
          <div
            style={{

            }}
            className="dashbottom"
          >
            <div>
              Showing {sortedAndFilteredData.length === 0 ? 0 : (currentPage - 1) * 60 + 1} to{" "}
              {currentPage * 60 <= sortedAndFilteredData.length ? currentPage * 60 : sortedAndFilteredData.length} of {sortedAndFilteredData.length}{" "}
              entries
            </div>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                marginTop: "10px",
              }}
              className="pagination"
            >
              <ul className="page">
                <li
                  className="page__btn newpage_btn"
                  style={{
                    padding: "1px 5px",
                    marginRight: "5px",
                    cursor: "pointer",
                    alignItems: "center",
                    color: "#32406d",
                  }}
                  onClick={() => {
                    if (id !== 1) {
                      setId(id - 1);  // Go to previous page if not the first page
                    } else {
                      toast.warn("You have reached the starting page already.", {
                        position: "top-right",
                        autoClose: 3000,
                        hideProgressBar: false,
                        closeOnClick: true,
                        pauseOnHover: true,
                        draggable: true,
                        progress: undefined,
                        theme: "dark",
                        transition: Bounce,
                      });  // Show warning toast if already on first page
                    }
                  }}
                >
                  <FaAngleLeft style={{ marginTop: "3px" }} />
                </li>
                <div className="gap" style={{ display: "flex", columnGap: "10px" }}>

                  {getPageRange().map((pageNumber, index) => (
                    <button
                      className={
                        pageNumber === id ? "pag_buttons" : "unsel_button"
                      }
                      key={index}
                      onClick={() => goToPage(pageNumber)}
                      style={{
                        fontWeight: pageNumber === id ? "bold" : "normal",
                        marginRight: "10px",
                        color: pageNumber === id ? "white" : "#000000", // Changed text color
                        backgroundColor:
                          pageNumber === id ? "#32406d" : "#ffff", // Changed background color
                        borderRadius: pageNumber === id ? "0.2rem" : "",
                        fontSize: "15px",
                        border: "none",
                        padding: "1px 10px", // Adjusted padding
                        cursor: "pointer", // Added cursor pointer
                      }}
                      class="page__numbers"
                    >
                      {pageNumber}
                    </button>
                  ))}

                </div>
                <li
                  className="page__btn newpage_btn"
                  style={{
                    padding: "1px 5px",
                    cursor: "pointer",
                    color: "#32406d",
                    marginLeft: "3px"
                  }}
                  onClick={() => {
                    if (belowCount > id * 60) setId(id + 1);
                    else {
                      toast.warn("Reached the end of the list", {
                        position: "top-right",
                        autoClose: 3000,
                        hideProgressBar: false,
                        closeOnClick: true,
                        pauseOnHover: true,
                        draggable: true,
                        progress: undefined,
                        theme: "dark",
                        transition: Bounce,
                      });
                      setId(id);
                    }
                  }}
                >
                  <FaAngleRight style={{ marginTop: "3px" }} />
                </li>
              </ul>
            </div>
          </div>

        ) : (
          <div
            style={{

            }}
            className="dashbottom"
          >
            <div>
              Showing {sortedAndFilteredData.length === 0 ? 0 : (currentPage - 1) * 60 + 1} to{" "}
              {currentPage * 60 <= sortedAndFilteredData.length ? currentPage * 60 : sortedAndFilteredData.length} of {sortedAndFilteredData.length}{" "}
              entries
            </div>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                marginTop: "10px",
              }}
              className="pagination"
            >
              <ul className="page">
                <li
                  className="page__btn newpage_btn"
                  style={{
                    padding: "1px 5px",
                    marginRight: "5px",
                    cursor: "pointer",
                    alignItems: "center",
                    color: "#32406d",
                  }}
                  onClick={() => {
                    currentPage !== 1 ? setCurrentPage(currentPage - 1) : setCurrentPage(currentPage);
                  }}
                >
                  <FaAngleLeft style={{ marginTop: "3px" }} />
                </li>
                <div className="gap" style={{ display: "flex", columnGap: "10px" }}>

                  {getPageRangesearch().map((pageNumber, index) => (

                    <button
                      className={
                        pageNumber === currentPage ? "pag_buttons" : "unsel_button"
                      }
                      key={index}
                      onClick={() => goToPagesearch(pageNumber)}

                      style={{
                        fontWeight: pageNumber === currentPage ? "bold" : "normal",
                        marginRight: "10px",
                        color: pageNumber === currentPage ? "white" : "#000000", // Changed text color
                        backgroundColor:
                          pageNumber === currentPage ? "#32406d" : "#ffff", // Changed background color
                        borderRadius: pageNumber === currentPage ? "0.2rem" : "",
                        fontSize: "15px",
                        border: "none",
                        padding: "1px 10px", // Adjusted padding
                        cursor: "pointer", // Added cursor pointer
                      }}
                      class="page__numbers"
                    >
                      {pageNumber}
                    </button>
                  ))}

                </div>
                <li
                  className="page__btn newpage_btn"
                  style={{
                    padding: "1px 5px",
                    cursor: "pointer",
                    color: "#32406d",
                    marginLeft: "3px"
                  }}
                  onClick={() => {
                    if (sortedAndFilteredData.length > currentPage * 60) setCurrentPage(currentPage + 1);
                    else {
                      toast.warn("Reached the end of the list", {
                        position: "top-right",
                        autoClose: 3000,
                        hideProgressBar: false,
                        closeOnClick: true,
                        pauseOnHover: true,
                        draggable: true,
                        progress: undefined,
                        theme: "dark",
                        transition: Bounce,
                      });
                      setCurrentPage(currentPage);
                    }
                  }}
                >
                  <FaAngleRight style={{ marginTop: "3px" }} />
                </li>
              </ul>
            </div>
          </div>
        )}

      </div>


      <ReactTooltip />


      <Modal
        isOpen={showEmailModal}
        onRequestClose={() => closeModal()}
        contentLabel="Recipient Email Selection"
        className="modal-content"
        overlayClassName="modal-overlay"
        style={{
          overlay: {

            zIndex: 9999,
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          },
          content: {
            width: "70%",
            // height: "auto",
            height: "95%", // Limit max height
            display: "flex",
            flexDirection: "column",
            background: "#ffffff",
            borderRadius: "10px",
            boxShadow: "0px 6px 20px rgba(0, 0, 0, 0.3)", // Softer shadow
            padding: "20px",
          },
        }}
      >
        <h2 style={{ margin: 0, marginBottom: '15px', textAlign: "left", fontWeight: '600', color: '#333' }}>Email Format</h2>
        <div style={{ marginBottom: '15px' }}>
          <label htmlFor="emailSubject" style={{ display: 'block', fontWeight: '500', marginBottom: '5px' }}>Subject</label>
          <input
            type="text"
            id="emailSubject"
            value={emailSubject}
            onChange={(e) => setEmailSubject(e.target.value)}
            placeholder="Enter subject"
            style={{
              width: '100%',
              padding: '10px',
              border: '1px solid #ddd',
              borderRadius: '4px',
              fontSize: '16px',
              outline: 'none',
            }}
          />
        </div>
       
        <div style={{ marginBottom: '15px' }}>
          <label htmlFor="emailBody" style={{ display: 'block', fontWeight: '500', marginBottom: '5px' }}>Body</label>
          <div
            id="emailBody"
            ref={bodyRef}
            contentEditable
            suppressContentEditableWarning
            style={{
              width: '100%',
              height: '360px',
              padding: '10px',
              border: 'none',
              borderRadius: '4px',
              fontSize: '16px',
              whiteSpace: 'pre-wrap',
              overflowY: 'auto',
            }}
          >
            Dear  <strong><span contentEditable="false" >{'{candidate_name}'}</span></strong>,<br />
            I hope this email finds you well. My name is <span contentEditable="false">{'{recruiter_name}'}</span>, and I am reaching out to you from Makonis Software Solutions. We recently reviewed your profile for an exciting job opportunity, and I wanted to see if you're open to exploring new roles.<br />
            We believe you could be a great fit for the position of <strong><span contentEditable="false">{'{job_role}'}</span></strong>. Below are some key details:<br />
            <table style={{
              margin: '20px auto',
              borderCollapse: 'collapse',
              width: '80%',
              boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
              fontFamily: 'Arial, sans-serif'
            }}>
              <thead>
                <tr style={{ backgroundColor: '#f0f0f0', color: '#333', textAlign: 'left' }}>
                  <th style={{ padding: '12px', borderBottom: '1px solid #ccc', fontSize: "19px", fontWeight: "600" }}>Role</th>
                  <th style={{ padding: '12px', borderBottom: '1px solid #ccc', fontSize: "19px", fontWeight: "600" }}>Location</th>
                  <th style={{ padding: '12px', borderBottom: '1px solid #ccc', fontSize: "19px", fontWeight: "600" }}>Mode</th>
                  {/* <th style={{ padding: '12px', borderBottom: '1px solid #ccc', fontSize: "19px", fontWeight: "900" }}>Budget</th> */}
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td style={{ padding: '10px', borderBottom: '1px solid #eee' }}>
                    <span contentEditable="false">{'{profile}'}</span>
                  </td>
                  <td style={{ padding: '10px', borderBottom: '1px solid #eee' }}>
                    <span contentEditable="false">{'{location}'}</span>
                  </td>
                  <td style={{ padding: '10px', borderBottom: '1px solid #eee' }}>
                    <span contentEditable="false">{'{mode}'}</span>
                  </td>
               
                </tr>
              </tbody>
            </table>

            
            If you're interested, I'd love to set up a time to discuss this further. Please let me know your availability or feel free to reply with any questions.<br /><br />
            Looking forward to hearing from you!<br /><br />
            Best regards,<br />
           
            <h4>Head - Talent Acquisition </h4>
            Makonis Software Solutions<br />

          </div>
        </div>


        <div style={{ display: 'flex', justifyContent: "right" }}>
          <button
            onClick={() => {
              setShowEmailModal(false)
              // setEmailSubject("")

            }}
            style={{
              backgroundColor: '#dc3545', // Red for cancel
              color: 'white',
              border: 'none',
              padding: '5px 15px',
              borderRadius: '4px',
              marginRight: "10px",
              cursor: 'pointer',
              transition: 'background-color 0.3s',
            }}
          >
            Cancel
          </button>
          <button
            onClick={handleEmailSend}
            style={{
              backgroundColor: '#28a745', // Green for success
              color: 'white',
              border: 'none',
              padding: '5px 15px',
              borderRadius: '4px',
              marginRight: '10px',
              width: "77px",
              cursor: 'pointer',
              transition: 'background-color 0.3s',
            }}
          >
            {waitForSubmissionemail ? "" : "Send"}
            <ThreeDots
              wrapperClass="ovalSpinner"
              wrapperStyle={{
                position: "relative",
                left: "5px"
              }}
              visible={waitForSubmissionemail}
              height="25"
              width="35"
              color="white"
              ariaLabel="oval-loading"
            />
          </button>

        </div>
      </Modal>

      <Modal
        isOpen={showprofiletransfer} // Control modal visibility
        onRequestClose={closeProfileTransfer} // Close modal when clicking outside
        contentLabel="ProfileTransfer"
          style={styles.modal}
      >
        <form  style={styles.form}>
       {/* Modal Header */}
        <div style={styles.header}>
          <h2 style={styles.title}>Profile Assign</h2>
          <button type="button" onClick={closeProfileTransfer} style={styles.closeButton}>
            &times;
          </button>
        </div>

        {/* Form Content */}
        <div style={styles.content}>
          <label htmlFor="recruiter" style={styles.label}>
            Assign to 
          </label>
          <select
            id="recruiter"
            name="recruiter"
            style={styles.select}
            value={selectedRecruiter}
            onChange={(e) => setSelectedRecruiter(e.target.value)}
            required
          >
            <option value="" disabled>
               Select  
            </option>
                {reviewerListToShow.some((user) => user.user_type === "management") && (
              <optgroup label="---Managers---">
                {reviewerListToShow
                  .filter((user) => user.user_type === "management")
                  .map((rec) => (
                    <option key={rec.id} value={rec.name}>
                      {rec.name}
                    </option>
                  ))}
              </optgroup>
            )}
            {reviewerListToShow.some((user) => user.user_type === "recruiter") && (
              <optgroup label="---Recruiters---">
                {reviewerListToShow
                  .filter((user) => user.user_type === "recruiter")
                  .map((rec) => (
                    <option key={rec.id} value={rec.name}>
                      {rec.name}
                    </option>
                  ))}
              </optgroup>
            )}
        
          </select>
        </div>

        {/* Modal Actions */}
        <div style={styles.actions}>
          <button
            type="button"
            onClick={closeProfileTransfer}
            style={styles.cancelButton}
          >
            Cancel
          </button>
         
                        <button
                          type="button"
                          onClick={handleTransfer}
                          style={styles.submitButton}
                          // value={waitForSubmissionprofile ? "" : "Transfer"}
                        >
                          {waitForSubmissionprofile ? "" : "Assign"}
                        <ThreeDots
                          wrapperClass="ovalSpinner"
                          wrapperStyle={{
                            position: "absolute",
                            top: "-3px",
                            left: "60px",
                          }}
                          visible={waitForSubmissionprofile}
                          height="45"
                          width="45"
                          color="white"
                          ariaLabel="oval-loading"
                        />
                  
                        </button>
                      </div>
      
        </form>
      </Modal>
    </div>

  );
}

export default ResumeFetch;

