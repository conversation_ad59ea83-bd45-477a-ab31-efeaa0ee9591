import React, { useState, useRef, memo, useEffect, useMemo } from "react";
import Modal from "react-modal";
import { useDispatch } from "react-redux";
import { IoMdAttach } from "react-icons/io";
import { useSelector } from "react-redux";
import { ThreeDots } from "react-loader-spinner";
import { setMeetings, setError } from "../store/slices/meetingslice";
import { getDashboardData } from "../Views/utilities";
import { fetchMeetings } from "../Views/utilities";
import { toast } from "react-toastify";
import teamsLogoPath from "../assets/teams-logo.jpeg";
import zoomLogoPath from "../assets/Zoom-logo.png";
import callLogoPath from "../assets/call-logo-new.png";
import InpersonPath from "../assets/Inperson-logo.png";
import 'react-phone-input-2/lib/style.css';
import PhoneInput from 'react-phone-input-2';
import { parsePhoneNumberFromString } from 'libphonenumber-js';

const ScheduleMeet = ({
  interviewModal,
  InterviewcloseModal,
  start_autoDate,
  end_autoDate,
  startautoTime,
  endautoTime,
  setShowCalendar,
  selectedScheduleData,
}) => {
  const dispatch = useDispatch();

  const { dashboardData } = useSelector((state) => state.dashboardSliceReducer);

  const candidateEmails =
    dashboardData.candidates?.map((candidates) => candidates.email) || [];
  const [meetingType, setMeetingType] = useState("");
  const [showForm, setShowForm] = useState(false);

  //  console.log(candidateEmails, "candidate emails");

  const { managers } = useSelector((state) => state.userSliceReducer);
  if (Array.isArray(managers)) {
    const emails = managers.map((manager) => manager.email);
    // console.log(emails, "emails");
  } else {
    console.log("Managers is not an array or is empty");
  }
  // console.log(managers, "managers");
  const { recruiters } = useSelector((state) => state.userSliceReducer);
  if (Array.isArray(recruiters)) {
    const recruiteremails = recruiters.map((recruiters) => recruiters.email);
    // console.log(recruiteremails, "recruiteremails");
  } else {
    console.log("recruiters is not an array or is empty");
  }
  // console.log(recruiters, "recruiters");
  const [title, setTitle] = useState("");
  const [selectedEmails1, setSelectedEmails1] = useState([]);
  const [selectedEmails2, setSelectedEmails2] = useState([]);
  const [isDropdownOpen, setIsDropdownOpen] = useState(null);
  const [inputValue, setInputValue] = useState("");
  const [suggestions, setSuggestions] = useState([]);
  const [startTime, setStartTime] = useState(startautoTime || "");
  const [endTime, setEndTime] = useState(endautoTime || "");
  const [isSuccessful, setIsSuccessful] = useState(false);
  const [responseSuccess, setResponseSuccess] = useState(false);

  const [showNewEvent, setShowNewEvent] = useState(false);
  const [eventName, setEventName] = useState("");
  const [eventPurpose, setEventPurpose] = useState("");
  const [eventDate, setEventDate] = useState("");
  const [email, setEmail] = useState("");
  const [location, setLocation] = useState("");
  const [mobile, setMobile] = useState("");
  const [countryData, setCountryData] = useState(null);
  const [eventFiles, setEventFiles] = useState([]);

  useEffect(() => {
    if (start_autoDate) setStartDate(start_autoDate);
    if (end_autoDate) setEndDate(end_autoDate);
    if (startautoTime) setStartTime(startautoTime);
    if (endautoTime) setEndTime(endautoTime);
  }, [start_autoDate, end_autoDate, startautoTime, endautoTime]);

  const dropdownRef1 = useRef(null);
  const dropdownRef2 = useRef(null);
  const inputRef1 = useRef(null);
  const inputRef2 = useRef(null);
  const dropdownRef = useRef(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMessage, setModalMessage] = useState("");
  const [selectedEmails, setSelectedEmails] = useState([]);

  const [waitForSubmission1, setwaitForSubmission1] = useState(false);
  const [waitForSubmission2, setwaitForSubmission2] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [showAddButton, setShowAddButton] = useState(false);
  const [newfilteredEmails, setnewFilteredEmails] = useState([]);
  const loggedInEmail = localStorage.getItem("email").toLowerCase();
    const [isValid, setIsValid] = useState(true);
  const [errorMsg, setErrorMsg] = useState('');
  const [country, setCountry] = useState('in');

  // Handle platform selection
  // Handle platform selection
  const handlePlatformSelect = (platform) => {
    setMeetingType(platform);
    if (platform === "teams" || platform === "zoom") {
      setShowForm(true);
    } else if (platform === "call" || platform === "Inperson") {
      setShowNewEvent(true); // Show the event form for call option
    }
  };

  const handleBackToPlatformSelection = () => {
    setShowForm(false);
    setShowNewEvent(false);
    setMeetingType("");
     resetForm();
    setEventName("");
    setEventPurpose("");
    setEventDate("");
    setMobile("");
    setMeetingType("");
    setEmail("");
        // resetForm();
  };

  const handleSearch = (e) => {
    const query = e.target.value;
    setSearchQuery(query);
    const filtered = candidateEmails.filter((email) =>
      email.toLowerCase().includes(query.toLowerCase())
    );
    setnewFilteredEmails(filtered);
    setShowAddButton(query && filtered.length === 0);
  };

  const handleAddEmail = () => {
    const trimmedQuery = searchQuery.trim();
    if (trimmedQuery && !selectedEmails.includes(trimmedQuery)) {
      setSelectedEmails([...selectedEmails, trimmedQuery]);
    }
    setSearchQuery("");
    setShowAddButton(false);
  };

  // console.log(candidateEmails, ":allcandidateschedule emails")
  const filteredEmails = candidateEmails.filter((email) =>
    email.toLowerCase().includes(searchQuery.toLowerCase())
  );
  //  console.log(filteredEmails, ":allcandidateschedule emails")

  const handleDropdownClick = (dropdown) => {
    setIsDropdownOpen(isDropdownOpen === dropdown ? null : dropdown);
  };
  const handleEmailChange = (email, dropdown) => {
    if (dropdown === "dropdown1") {
      setSelectedEmails1((prev) => {
        if (!Array.isArray(prev)) return [email]; // Ensure prev is an array
        return prev.includes(email)
          ? prev.filter((e) => e !== email)
          : [...prev, email];
      });
    } else if (dropdown === "dropdown2") {
      setSelectedEmails2((prev) => {
        if (!Array.isArray(prev)) return [email]; // Ensure prev is an array
        return prev.includes(email)
          ? prev.filter((e) => e !== email)
          : [...prev, email];
      });
      setSearchQuery2("");
      setIsDropdownOpen2(false);
    }
  };
//   mobile number validation

 const handleChange = (value, data) => {
    setMobile(value);
    setCountry(data.countryCode);

    const phoneNumber = parsePhoneNumberFromString(`+${value}`);
    if (phoneNumber) {
      const valid = phoneNumber.isValid();
      setIsValid(valid);

      if (!valid) {
        const exampleNumber = phoneNumber.countryCallingCode + ' ' + phoneNumber.formatNational();
        setErrorMsg(`Invalid number for ${data.name}.`);
      } else {
        setErrorMsg('');
      }
    } else {
      setIsValid(false);
      setErrorMsg('Invalid phone number.');
    }
  };
  const handleTimeZoneChange = (e) => {
    const newTimeZone = e.target.value;
    setSelectedTimeZone(newTimeZone);
    console.log("Selected Time Zone:", newTimeZone); // Log the new value
  };
  const [selectedTimeZone, setSelectedTimeZone] = useState("");

  const syncEvents = async () => {
    try {
      const response = await fetch("  http://************:5002/sync_events", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          recruiter_email: localStorage.getItem("email"),
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to sync events");
      }

      const data = await response.json();
      console.log("Sync events response:", data);
    } catch (error) {
      console.error("Sync error:", error);
      setError(error.message);
    }
  };
  const [description, setDescription] = useState("");
  const handleDescriptionChange = (e) => {
    setDescription(e.target.value);
  };
  const [files, setFiles] = useState([]);
  const fileToBase64 = (file) =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result;
        const base64 = result.split(",")[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  const handleFileChange = (e) => {
    setFiles(Array.from(e.target.files));
  };
  const handleRemoveFile = (indexToRemove) => {
    const updatedFiles = files.filter((_, index) => index !== indexToRemove);
    setFiles(updatedFiles);
  };
  //  console.log(fileToBase64, "fileToBase64");
  // console.log(selectedScheduleData,"ddwqfwwrwr")
  useEffect(() => {
    if (selectedScheduleData) {
      // If user clicked from dashboard, prefill title

      console.log(selectedScheduleData.profile, "daafwuw");
      console.log(selectedScheduleData.client, "daafwuw");
      console.log(selectedScheduleData.status, "daafwuw");
      setTitle(
        `Meeting | ${selectedScheduleData.client || ""} | ${selectedScheduleData.profile || ""} | ${selectedScheduleData.status || ""}`
      );
    } else {
      // If user clicked from calendar, allow manual typing
      setTitle("");
    }
  }, [selectedScheduleData]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (
      !title ||
      !meetingType ||
      !startDate ||
      !endDate ||
      !startTime ||
      !endTime ||
      !selectedTimeZone ||
      selectedEmails.length === 0
    ) {
      toast.error("Please fill in all required fields.");
      return;
    }
    const requiredAttendees = selectedEmails;
    const optionalAttendees = selectedEmails2;
    const filePayload = await Promise.all(
      files.map(async (file) => ({
        file_name: file.name,
        file_content_base64: await fileToBase64(file),
      }))
    );
    console.log(filePayload, "attachments");
    if (!waitForSubmission1) {
      setwaitForSubmission1(true);
      const apiEndpoint =
        meetingType === "teams"
          ? "  http://************:5002/create_event"
          : meetingType === "zoom"
            ? "  http://************:5002/schedule_zoom_meeting"
            : "";
      try {
        const response = await fetch(apiEndpoint, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer YOUR_ACCESS_TOKEN`,
          },
          body: JSON.stringify({
            subject: title,
            start_date: startDate,
            start_time: startTime,
            end_date: endDate,
            end_time: endTime,
            attendees: requiredAttendees,
            cc_recipients: optionalAttendees,
            time_zone: selectedTimeZone,
            recruiter_email: localStorage.getItem("email"),
            recruiter_id: localStorage.getItem("user_id"),
            description: description,
            files: filePayload,
            // attachments
          }),
        });

        if (response.ok) {
          setModalMessage("Meeting scheduled successfully!");
          setIsModalOpen(true);
          InterviewcloseModal();
          setwaitForSubmission1(false);
          setIsSuccessful(true);
          setTitle("");
          setStartDate("");
          setEndDate("");
          setStartTime("");
          setMeetingType("");
          setEndTime("");
          setFiles([]);
          setSelectedTimeZone("");
          setSelectedEmails1([]);
          setSelectedEmails2([]);
          setDescription("");
          setResponseSuccess(true);
          await syncEvents();
          await fetchMeetings();
        } else {
          setModalMessage("Failed to schedule the meeting.");
          setIsModalOpen(true);
          setwaitForSubmission1(false);
          setIsSuccessful(false);
          setResponseSuccess(false);
        }
      } catch (error) {
        setModalMessage("An error occurred while scheduling the meeting.");
        setIsModalOpen(true);
        setwaitForSubmission1(false);
        setIsSuccessful(false);
        setResponseSuccess(false);
        console.error("Error:", error);
      }
    }
  };

  const handleNewEventSubmit = async (e) => {
    e.preventDefault();
    if (waitForSubmission2) return;

    setwaitForSubmission2(true);

    // Validate common fields
    if (!eventName || !eventPurpose || !startDate) {
      toast.error("Please fill in all required fields.");
      setwaitForSubmission2(false);
      return;
    }

    // Prepare payload and endpoint based on meeting type
    let endpoint = "";
    let formattedEvent = {};
     const optionalAttendees = selectedEmails2;
    if (meetingType === "call") {
      // Validation for Call
      if (!mobile) {
        toast.error("Please enter a phone number.");
        setwaitForSubmission2(false);
        return;
      }
       const parsedNumber = parsePhoneNumberFromString("+" + mobile);
  if (!parsedNumber || !parsedNumber.isValid()) {
    // toast.error("Invalid phone number.");
    setwaitForSubmission2(false);
    return;
  }

      endpoint = "http://************:5002/call_event";
      formattedEvent = {
        name: eventName,
        purpose: eventPurpose,
        mobile: mobile,
        date: startDate,
        time: startTime,
        user_id: localStorage.getItem("user_id"),
        user_email: localStorage.getItem("email"),
      };
    } else if (meetingType === "Inperson") {
      // Validation for Inperson
      if (!startTime || !email || !location) {
        toast.error("Please fill in all In-Person fields (start time, email, location).");
        setwaitForSubmission2(false);
        return;
      }

      endpoint = "http://************:5002/in_person_event";
      formattedEvent = {
        name: eventName,
        purpose: eventPurpose,
        time: startTime,
        date: startDate,
        cc_recipients: optionalAttendees,
        email: email,
        location: location,
        user_id: localStorage.getItem("user_id"),
        user_email: localStorage.getItem("email"),
      };
    } else {
      toast.error("Unsupported meeting type.");
      setwaitForSubmission2(false);
      return;
    }

    try {
      const response = await fetch(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer YOUR_ACCESS_TOKEN`, // Replace if needed
        },
        body: JSON.stringify(formattedEvent),
      });

      if (response.ok) {
        const data = await response.json();
        toast.success(data.message || "Event created successfully!");

        // Reset all fields
        setShowNewEvent(false);
        setEventName("");
        setEventPurpose("");
        setStartDate("");
        setStartTime("");
        setMobile("");
        setEmail("");
        setLocation("");
        handleModalClose();
      } else {
        toast.error("Failed to create the event.");
      }
    } catch (error) {
      console.error("Error creating event:", error);
      toast.error("An error occurred while creating the event.");
    } finally {
      setwaitForSubmission2(false);
    }
  };

  const [startDate, setStartDate] = useState(() => {
    const today = new Date().toISOString().split("T")[0];
    return today;
  });
  const [endDate, setEndDate] = useState("");
  const [error, seterror] = useState("");
  useEffect(() => {
    if (startDate) {
      setEndDate(startDate);
    }
  }, [startDate]);

  const timeOptions = Array.from({ length: 24 * 2 }, (_, i) => {
    const hour = Math.floor(i / 2);
    const minute = (i % 2) * 30;
    return {
      value: `${hour.toString().padStart(2, "0")}:${minute.toString().padStart(2, "0")}`,
      label: `${hour}:${minute.toString().padStart(2, "0")}`,
    };
  });

  const add30Minutes = (time) => {
    const [hours, minutes] = time.split(":").map(Number);
    let newMinutes = minutes + 30;
    let newHours = hours;

    if (newMinutes >= 60) {
      newMinutes -= 60;
      newHours += 1;
    }

    if (newHours === 24) {
      newHours = 0;
    }

    return `${String(newHours).padStart(2, "0")}:${String(newMinutes).padStart(2, "0")}`;
  };

  useEffect(() => {
    if (startTime) {
      setEndTime(add30Minutes(startTime));
    } else {
      setEndTime("");
    }
  }, [startTime]);

  const [searchQuery2, setSearchQuery2] = useState("");

  const handleCheckboxChangeEmails = (email) => {
    if (!selectedEmails.includes(email)) {
      setSelectedEmails([...selectedEmails, email]);
    } else {
      setSelectedEmails(selectedEmails.filter((e) => e !== email));
    }
    setSearchQuery(""); // Clear search query
    setIsDropdownOpen(false); // Close the dropdown
    setDropdownVisible(false); // Ensure dropdown is closed
  };

  const handleStartDateChange = (e) => {
    const selectedStartDate = new Date(e.target.value);
    const today = new Date();

    // Set the time to midnight for accurate comparison
    today.setHours(0, 0, 0, 0);
    selectedStartDate.setHours(0, 0, 0, 0);

    // Get current year and month
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth();

    // Get the selected year and month
    const selectedYear = selectedStartDate.getFullYear();
    const selectedMonth = selectedStartDate.getMonth();

    // Validation
    if (
      selectedStartDate < today || // Cannot be in the past
      selectedYear < currentYear || // Cannot be from a previous year
      (selectedYear === currentYear && selectedMonth < currentMonth) // Cannot be from a previous month in the current year
    ) {
      setError(
        "Start Date must be today or a future date in this month, next month, or next year."
      );
      toast.error(
        "Start Date must be today or a future date in this month, next month, or next year."
      );
    } else {
      setError("");
      setStartDate(e.target.value);
    }
  };

  const handleEndDateChange = (e) => {
    const newEndDate = e.target.value;
    setEndDate(newEndDate);

    if (new Date(startDate) > new Date(newEndDate)) {
      seterror("End Date is Earlier than Start Date .");
      toast.error("End Date is Earlier than Start Date.");
    } else {
      seterror(""); // Clear error if validation passes
    }
  };

  const resetForm = () => {
    setTitle("");
    setSelectedEmails([]);
    setSearchQuery("");
    setSearchQuery2("");
    setSelectedEmails2([]);
    setSelectedTimeZone("");
    setError("");
    setFiles([]);
    setStartTime("");
    setEndTime("");
    setStartDate("");
    setEndDate("");
    setShowForm(false);
    setShowNewEvent(false);
    setMeetingType("");
  };
  const handleModalClose = () => {
    InterviewcloseModal();
    setShowForm(false);
    setShowNewEvent(false)
    resetForm();
    setEventName("");
    setEventPurpose("");
    setEventDate("");
    setMobile("");
    setMeetingType("");
    setEmail("");
  };
  const emailRegex =
    /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(\.[a-zA-Z]{2,})*$/;

  // Function to validate email before adding
  const validateEmail = (email) => {
    return emailRegex.test(email);
  };

  const handleAddEmailWithValidation = () => {
    if (validateEmail(searchQuery)) {
      handleAddEmail();
    } else {
      toast.warn("Please enter a valid email address");
    }
  };
  const handleAddEmailWithValidations = () => {
    if (validateEmail(searchQuery2)) {
      handleEmailChange(searchQuery2, "dropdown2");
      setSearchQuery2("");
    } else {
      toast.warn("Please enter a valid email address");
    }
  };

  useEffect(() => {
    if (selectedScheduleData?.email) {
      const email = selectedScheduleData.email.toLowerCase();
      // Avoid duplicates
      if (!selectedEmails.includes(email)) {
        setSelectedEmails((prev) => [...prev, email]);
      }
      // setSearchQuery(email); // Pre-fill search box with the selected email
    }
  }, [selectedScheduleData]);

  return (
    <div>
      <Modal
        isOpen={interviewModal}
        onRequestClose={handleModalClose}
        contentLabel="Calendar Modal"
        style={{
          overlay: {
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            zIndex: 9999,
          },
          content: {
            color: "#333",
            top: "50%",
            left: "50%",
            right: "auto",
            bottom: "auto",
            marginRight: "-50%",
            transform: "translate(-50%, -50%)",
            width: showNewEvent
              ? "400px"
              : !showForm
                ? "350px"
                : window.innerWidth <= 542
                  ? "100%"
                  : "550px",
            maxHeight: showNewEvent ? "630px" : !showForm ? "400px" : "630px",
            padding: "0px",
            borderRadius: "12px",
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.15)",
            position: "relative",
            background: "linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)",
          },
        }}
      >
        {!showNewEvent ? (
          <>
            {!showForm ? (
              // Platform Selection Screen
              <div style={{ padding: "20px" }}>
                <div style={{ textAlign: "center", marginBottom: "8px" }}>
                  <h2
                    style={{
                      color: "#32406D",
                      fontSize: "22px",
                      fontWeight: "600",
                      margin: "0 0 8px 0",
                    }}
                  >
                    Choose Platform
                  </h2>
                  <p
                    style={{
                      color: "#666",
                      fontSize: "14px",
                      margin: "0",
                      opacity: 0.8,
                    }}
                  >
                    Select your preferred meeting platform
                  </p>
                </div>

                <div
                  style={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    gap: "20px",
                    marginBottom: "10px",
                    flexWrap: "wrap",
                  }}
                >
                  {/* Teams Button */}
                  <div
                    onClick={() => handlePlatformSelect("teams")}
                    style={{
                      width: "60px",
                      height: "60px",
                      backgroundColor: "white",
                      border: "3px solid #e9ecef",
                      borderRadius: "16px",
                      cursor: "pointer",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      transition: "all 0.3s ease",
                      boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
                      position: "relative",
                      overflow: "hidden",
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = "translateY(-3px)";
                      e.currentTarget.style.borderColor = "#403886ff";
                      e.currentTarget.style.boxShadow =
                        "0 6px 20px rgba(70, 78, 184, 0.2)";
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = "translateY(0)";
                      e.currentTarget.style.borderColor = "#e9ecef";
                      e.currentTarget.style.boxShadow =
                        "0 2px 8px rgba(0, 0, 0, 0.1)";
                    }}
                  >
                    <img
                      src={teamsLogoPath}
                      alt="Teams"
                      style={{
                        width: "45px",
                        height: "45px",
                        objectFit: "contain",
                      }}
                    />
                  </div>

                  {/* Zoom Button */}
                  <div
                    onClick={() => handlePlatformSelect("zoom")}
                    style={{
                      width: "60px",
                      height: "60px",
                      backgroundColor: "white",
                      border: "3px solid #e9ecef",
                      borderRadius: "16px",
                      cursor: "pointer",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      transition: "all 0.3s ease",
                      boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
                      position: "relative",
                      overflow: "hidden",
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = "translateY(-3px)";
                      e.currentTarget.style.borderColor = "#403886ff";
                      e.currentTarget.style.boxShadow =
                        "0 6px 20px rgba(45, 140, 255, 0.2)";
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = "translateY(0)";
                      e.currentTarget.style.borderColor = "#e9ecef";
                      e.currentTarget.style.boxShadow =
                        "0 2px 8px rgba(0, 0, 0, 0.1)";
                    }}
                  >
                    <img
                      src={zoomLogoPath}
                      alt="Zoom"
                      style={{
                        width: "45px",
                        height: "45px",
                        objectFit: "contain",
                      }}
                    />
                  </div>

                  {/* Call Button */}
                  <div
                    onClick={() => handlePlatformSelect("call")}
                    style={{
                      width: "60px",
                      height: "60px",
                      backgroundColor: "white",
                      border: "3px solid #e9ecef",
                      borderRadius: "16px",
                      cursor: "pointer",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      transition: "all 0.3s ease",
                      boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
                      position: "relative",
                      overflow: "hidden",
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = "translateY(-3px)";
                      e.currentTarget.style.borderColor = "#403886ff";
                      e.currentTarget.style.boxShadow =
                        "0 6px 20px rgba(40, 167, 69, 0.2)";
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = "translateY(0)";
                      e.currentTarget.style.borderColor = "#e9ecef";
                      e.currentTarget.style.boxShadow =
                        "0 2px 8px rgba(0, 0, 0, 0.1)";
                    }}
                  >
                    <img
                      src={callLogoPath}
                      alt="Call"
                      style={{
                        width: "45px",
                        height: "45px",
                        objectFit: "contain",
                      }}
                    />
                  </div>
                  <div
                    onClick={() => handlePlatformSelect("Inperson")}
                    style={{
                      width: "60px",
                      height: "60px",
                      backgroundColor: "white",
                      border: "3px solid #e9ecef",
                      borderRadius: "16px",
                      cursor: "pointer",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      transition: "all 0.3s ease",
                      boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
                      position: "relative",
                      overflow: "hidden",
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = "translateY(-3px)";
                      e.currentTarget.style.borderColor = "#403886ff";
                      e.currentTarget.style.boxShadow =
                        "0 6px 20px rgba(42, 40, 167, 0.2)";
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = "translateY(0)";
                      e.currentTarget.style.borderColor = "#e9ecef";
                      e.currentTarget.style.boxShadow =
                        "0 2px 8px rgba(0, 0, 0, 0.1)";
                    }}
                  >
                    <img
                      src={InpersonPath}
                      alt="Inperson"
                      style={{
                        width: "45px",
                        height: "45px",
                        objectFit: "contain",
                      }}
                    />
                  </div>
                </div>

                <div
                  style={{
                    display: "flex",
                    justifyContent: "center",
                    gap: "15px",
                    flexWrap: "wrap",
                  }}
                >
                  <button
                    onClick={handleModalClose}
                    style={{
                      backgroundColor: "#dc3545",
                      color: "white",
                      border: "none",
                      padding: "10px 18px",
                      borderRadius: "8px",
                      cursor: "pointer",
                      fontSize: "14px",
                      fontWeight: "500",
                      transition: "all 0.3s ease",
                      minWidth: "100px",
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.backgroundColor = "#c82333";
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.backgroundColor = "#dc3545";
                    }}
                  >
                    Close
                  </button>
                </div>
              </div>
            ) : (
              // Enhanced Meeting Form
              <form onSubmit={handleSubmit}>
                <div
                  style={{
                    padding: "0px 20px",
                    margin: "8px",
                    background: "white",
                    borderRadius: "12px",
                    margin: "8px",
                    boxShadow: "0 2px 8px rgba(0,0,0,0.05)",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                      marginBottom: "25px",
                      borderBottom: "2px solid #e9ecef",
                      backgroundColor: "#fff",
                      zIndex: 100,
                      position: "sticky",
                      top: "0px",
                      zIndex: 10,
                      padding: "20px 0px",
                    }}
                  >
                    <button
                      type="button"
                      onClick={handleBackToPlatformSelection}
                      style={{
                        backgroundColor: "transparent",
                        border: "none",
                        color: "black",
                        cursor: "pointer",
                        fontSize: "30px",
                        padding: "1px",
                      }}
                    >
                      ←
                    </button>
                    <h2
                      style={{
                        color: "#32406D",
                        fontSize: "20px",
                        textAlign: "center",
                        margin: "0",
                        fontWeight: "600",
                      }}
                    >
                      New {meetingType === "teams" ? "Teams" : "Zoom"} Meeting
                    </h2>
                    <div style={{ width: "30px" }}></div>{" "}
                    {/* Spacer for centering */}
                  </div>

                  {/* Title Input with enhanced styling */}
                  <div style={{ marginBottom: "20px" }}>
                    <label
                      style={{
                        display: "block",
                        marginBottom: "8px",
                        fontSize: "16px",
                        textAlign: "left",
                        fontWeight: "500",
                        color: "#495057",
                      }}
                    >
                      Title *
                    </label>
                    <input
                      type="text"
                      placeholder="Add title"
                      value={title}
                      onChange={(e) => setTitle(e.target.value)}
                      style={{
                        width: "100%",
                        height: "45px",
                        borderRadius: "8px",
                        border: "2px solid #dee2e6",
                        paddingLeft: "15px",
                        fontSize: "16px",
                        transition: "border-color 0.3s ease",
                        outline: "none",
                      }}
                      onFocus={(e) => (e.target.style.borderColor = "#32406D")}
                      onBlur={(e) => (e.target.style.borderColor = "#dee2e6")}
                    />
                  </div>

                  {/* Enhanced Attendees Section */}
                  <div style={{ marginBottom: "20px" }}>
                    <label
                      style={{
                        display: "block",
                        marginBottom: "8px",
                        fontSize: "16px",
                        textAlign: "left",
                        fontWeight: "500",
                        color: "#495057",
                      }}
                    >
                      Attendees *
                    </label>
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        flexWrap: "nowrap",
                        border: "2px solid #dee2e6",
                        borderRadius: "8px",
                        padding: "8px",
                        overflowX: "auto",
                        overflowY: "hidden",
                        whiteSpace: "nowrap",
                        fontSize: "12px",
                        minHeight: "45px",
                        position: "sticky",
                        right: "0px",
                        background: "white",
                      }}
                    >
                      {selectedEmails.map((email, index) => (
                        <div
                          key={index}
                          style={{
                            display: "inline-flex",
                            alignItems: "center",
                            backgroundColor: "#32406D",
                            color: "white",
                            padding: "6px 12px",
                            borderRadius: "20px",
                            marginRight: "8px",
                            fontSize: "14px",
                            marginBottom: "0px"
                          }}
                        >
                          {email}
                          <span
                            onClick={() =>
                              setSelectedEmails(
                                selectedEmails.filter((e) => e !== email)
                              )
                            }
                            style={{
                              marginLeft: "8px",
                              cursor: "pointer",
                              fontWeight: "bold",
                              fontSize: "16px",
                            }}
                          >
                            ×
                          </span>
                        </div>
                      ))}
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          flex: "1",
                          position: "relative",
                        }}
                      >
                        <input
                          type="text"
                          placeholder="Search or add email"
                          value={searchQuery}
                          onChange={handleSearch}
                          style={{
                            flex: "1",
                            minWidth: "200px",
                            border: "none",
                            paddingLeft: "10px",
                            fontSize: "16px",
                            outline: "none",
                            width: "auto",
                          }}
                        />
                        {searchQuery && filteredEmails.length === 0 && (
                          <button
                            type="button"
                            onClick={handleAddEmailWithValidation}
                            style={{
                              position: "sticky",
                              right: "0px",
                              backgroundColor: "#32406D",
                              color: "white",
                              border: "none",
                              padding: "6px 12px",
                              borderRadius: "6px",
                              whiteSpace: "nowrap",
                              zIndex: 1,
                              fontSize: "14px",
                            }}
                          >
                            Add
                          </button>
                        )}
                      </div>
                    </div>

                    {/* Enhanced dropdown */}
                    {searchQuery && (
                      <div
                        className="dropdown-menu"
                        style={{
                          border: "2px solid #dee2e6",
                          borderRadius: "8px",
                          boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
                          marginTop: "5px",
                          width: "auto",
                          background: "white",
                          maxHeight: "200px",
                          overflowY: "auto",
                        }}
                      >
                        {[
                          ...new Set([
                            ...filteredEmails.map((email) =>
                              email.toLowerCase()
                            ),
                          ]),
                        ]
                          .filter(
                            (email) =>
                              email.includes(searchQuery.toLowerCase()) &&
                              email !== loggedInEmail
                          )
                          .map((email, index) => (
                            <label
                              key={index}
                              className="dropdown-item"
                              style={{
                                display: "flex",
                                padding: "12px",
                                fontWeight: "normal",
                                cursor: "pointer",
                                borderBottom: "1px solid #f8f9fa",
                                transition: "background-color 0.2s ease",
                              }}
                              onClick={() => {
                                handleCheckboxChangeEmails(email);
                                handleSelectCheckedEmails();
                              }}
                              onMouseEnter={(e) =>
                                (e.target.style.backgroundColor = "#f8f9fa")
                              }
                              onMouseLeave={(e) =>
                                (e.target.style.backgroundColor = "white")
                              }
                            >
                              <input
                                type="checkbox"
                                checked={selectedEmails.includes(email)}
                                onChange={() =>
                                  handleEmailChange(email, "attendees")
                                }
                                style={{ marginRight: "12px" }}
                              />
                              {email}
                            </label>
                          ))}
                      </div>
                    )}
                  </div>

                  {/* Enhanced Optional Attendees Section */}
                  <div style={{ marginBottom: "20px" }}>
                    <label
                      style={{
                        display: "block",
                        marginBottom: "8px",
                        fontSize: "16px",
                        textAlign: "left",
                        fontWeight: "500",
                        color: "#495057",
                      }}
                    >
                      Optional Attendees
                    </label>
                    <div ref={dropdownRef2} style={{ position: "relative" }}>
                      <div
                        style={{
                          display: "flex",
                          flexWrap: "nowrap",
                          alignItems: "center",
                          border: "2px solid #dee2e6",
                          borderRadius: "8px",
                          padding: "8px",
                          fontSize: "12px",
                          position: "relative",
                          overflowX: "auto",
                          minHeight: "45px",
                          background: "white",
                        }}
                      >
                        {selectedEmails2?.map((email, index) => (
                          <div
                            key={index}
                            style={{
                              display: "inline-flex",
                              alignItems: "center",
                              backgroundColor: "#6c757d",
                              color: "white",
                              padding: "6px 12px",
                              borderRadius: "20px",
                              marginRight: "8px",
                              fontSize: "14px",
                              marginBottom: "0px"
                            }}
                          >
                            {email}
                            <span
                              onClick={() =>
                                handleEmailChange(email, "dropdown2")
                              }
                              style={{
                                marginLeft: "8px",
                                cursor: "pointer",
                                fontWeight: "bold",
                                fontSize: "16px",
                              }}
                            >
                              ×
                            </span>
                          </div>
                        ))}
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            flex: "1",
                          }}
                        >
                          <input
                            type="text"
                            placeholder="Search or add email"
                            onClick={() => handleDropdownClick("dropdown2")}
                            onChange={(e) => setSearchQuery2(e.target.value)}
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                handleEmailChange(e.target.value, "dropdown2");
                                setSearchQuery2("");
                              }
                            }}
                            value={searchQuery2}
                            style={{
                              flex: "1",
                              minWidth: "150px",
                              border: "none",
                              paddingLeft: "10px",
                              fontSize: "16px",
                              outline: "none",
                            }}
                            ref={inputRef2}
                          />
                          {searchQuery2 &&
                            ![...managers, ...recruiters].some((item) =>
                              item.email
                                .toLowerCase()
                                .includes(searchQuery2.toLowerCase())
                            ) && (
                              <button
                                onClick={handleAddEmailWithValidations}
                                style={{
                                  position: "absolute",
                                  right: "10px",
                                  backgroundColor: "#32406D",
                                  color: "white",
                                  border: "none",
                                  padding: "6px 12px",
                                  borderRadius: "6px",
                                  fontSize: "14px",
                                }}
                              >
                                Add
                              </button>
                            )}
                        </div>
                      </div>
                      {searchQuery2 && (
                        <div
                          className="dropdown-menu"
                          style={{
                            border: "2px solid #dee2e6",
                            borderRadius: "8px",
                            boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
                            marginTop: "5px",
                            width: "auto",
                            background: "white",
                            maxHeight: "200px",
                            overflowY: "auto",
                          }}
                        >
                          {[
                            ...new Set([
                              ...managers.map((item) =>
                                item.email.toLowerCase()
                              ),
                              ...recruiters.map((item) =>
                                item.email.toLowerCase()
                              ),
                            ]),
                          ]
                            .filter(
                              (email) =>
                                email.includes(searchQuery2.toLowerCase()) &&
                                email !== loggedInEmail
                            )
                            .map((email, index) => (
                              <label
                                key={index}
                                className="dropdown-item"
                                style={{
                                  display: "flex",
                                  padding: "12px",
                                  fontWeight: "normal",
                                  cursor: "pointer",
                                  borderBottom: "1px solid #f8f9fa",
                                  transition: "background-color 0.2s ease",
                                }}
                                onMouseEnter={(e) =>
                                  (e.target.style.backgroundColor = "#f8f9fa")
                                }
                                onMouseLeave={(e) =>
                                  (e.target.style.backgroundColor = "white")
                                }
                              >
                                <input
                                  type="checkbox"
                                  checked={selectedEmails2?.includes(email)}
                                  onChange={() =>
                                    handleEmailChange(email, "dropdown2")
                                  }
                                  style={{ marginRight: "12px" }}
                                />
                                {email}
                              </label>
                            ))}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Enhanced Description Section */}
                  {/* Enhanced Description Section with auto-expand */}
                  <div style={{ marginBottom: "20px" }}>
                    <label
                      style={{
                        display: "block",
                        marginBottom: "8px",
                        fontSize: "16px",
                        textAlign: "left",
                        fontWeight: "500",
                        color: "#495057",
                      }}
                    >
                      Description
                    </label>
                    <textarea
                      placeholder="Enter meeting description..."
                      style={{
                        width: "100%",
                        border: "2px solid #dee2e6",
                        borderRadius: "8px",
                        paddingLeft: "15px",
                        paddingTop: "12px",
                        fontSize: "16px",
                        resize: "none", // Disable manual resize
                        outline: "none",
                        transition: "border-color 0.3s ease",
                        minHeight: "50px", // Minimum height
                        overflow: "hidden", // Hide scrollbar
                      }}
                      value={description}
                      onChange={handleDescriptionChange}
                      onFocus={(e) => (e.target.style.borderColor = "#32406D")}
                      onBlur={(e) => (e.target.style.borderColor = "#dee2e6")}
                      onInput={(e) => {
                        // Auto-expand functionality
                        e.target.style.height = "auto";
                        e.target.style.height =
                          Math.max(80, e.target.scrollHeight) + "px";
                      }}
                    />
                  </div>

                  {/* Enhanced Date and Time Section */}
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      marginBottom: "20px",
                      gap: "15px",
                    }}
                  >
                    <div style={{ width: "48%" }}>
                      <label
                        style={{
                          display: "block",
                          marginBottom: "8px",
                          fontSize: "16px",
                          textAlign: "left",
                          fontWeight: "500",
                          color: "#495057",
                        }}
                      >
                        Start Date *
                      </label>
                      <input
                        type="date"
                        value={startDate}
                        onChange={handleStartDateChange}
                        style={{
                          width: "100%",
                          height: "45px",
                          borderRadius: "8px",
                          border: "2px solid #dee2e6",
                          paddingLeft: "15px",
                          fontSize: "16px",
                          outline: "none",
                        }}
                      />
                    </div>
                    <div style={{ width: "48%" }}>
                      <label
                        style={{
                          display: "block",
                          marginBottom: "8px",
                          fontSize: "16px",
                          textAlign: "left",
                          fontWeight: "500",
                          color: "#495057",
                        }}
                      >
                        End Date *
                      </label>
                      <input
                        type="date"
                        value={endDate}
                        onChange={handleEndDateChange}
                        style={{
                          width: "100%",
                          height: "45px",
                          borderRadius: "8px",
                          border: "2px solid #dee2e6",
                          paddingLeft: "15px",
                          fontSize: "16px",
                          outline: "none",
                        }}
                      />
                      {error && (
                        <p
                          style={{
                            color: "red",
                            fontSize: "14px",
                            marginTop: "5px",
                          }}
                        >
                          {error}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Enhanced Time Zone Section */}
                  <div style={{ marginBottom: "20px" }}>
                    <label
                      style={{
                        display: "block",
                        marginBottom: "8px",
                        fontSize: "16px",
                        textAlign: "left",
                        fontWeight: "500",
                        color: "#495057",
                      }}
                    >
                      Time Zone *
                    </label>
                    <select
                      value={selectedTimeZone}
                      onChange={handleTimeZoneChange}
                      style={{
                        width: "100%",
                        height: "45px",
                        borderRadius: "8px",
                        border: "2px solid #dee2e6",
                        paddingLeft: "15px",
                        fontSize: "16px",
                        outline: "none",
                        background: "white",
                      }}
                    >
                      <option value="">Select a time zone</option>
                      <option value="Asia/Kolkata">Asia/Kolkata</option>
                      <option value="America/New_York">America/New_York</option>
                      <option value="Europe/London">Europe/London</option>
                      <option value="Australia/Sydney">Australia/Sydney</option>
                      <option value="Asia/Tokyo">Asia/Tokyo</option>
                    </select>
                  </div>

                  {/* Enhanced Attachment Section */}
                  <div style={{ marginBottom: "20px" }}>
                    <label
                      style={{
                        display: "block",
                        marginBottom: "8px",
                        fontSize: "16px",
                        textAlign: "left",
                        fontWeight: "500",
                        color: "#495057",
                      }}
                    >
                      Attachments{" "}
                      {files.length > 0 && (
                        <span
                          style={{
                            fontSize: "14px",
                            color: "#6c757d",
                            fontWeight: "normal",
                          }}
                        >
                          ({files.length} file{files.length > 1 ? "s" : ""}{" "}
                          selected)
                        </span>
                      )}
                    </label>
                    <input
                      type="file"
                      multiple
                      onChange={handleFileChange}
                      accept=".pdf,.doc,.docx"
                      style={{
                        width: "100%",
                        padding: "12px",
                        borderRadius: "8px",
                        border: "2px solid #dee2e6",
                        fontSize: "14px",
                        cursor: "pointer",
                        background: "white",
                      }}
                    />
                    {files.length > 0 && (
                      <div
                        style={{
                          marginTop: "10px",
                          padding: "10px",
                          background: "#f8f9fa",
                          borderRadius: "8px",
                          maxHeight: "120px",
                          overflowY: "auto",
                        }}
                      >
                        {files.map((file, index) => (
                          <div
                            key={index}
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                              padding: "8px",
                              marginBottom: "4px",
                              background: "white",
                              borderRadius: "6px",
                              fontSize: "14px",
                            }}
                          >
                            <span>{file.name}</span>
                            <button
                              type="button"
                              onClick={() => handleRemoveFile(index)}
                              style={{
                                background: "#dc3545",
                                border: "none",
                                color: "white",
                                borderRadius: "4px",
                                cursor: "pointer",
                                fontSize: "12px",
                                padding: "4px 8px",
                              }}
                            >
                              Remove
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Enhanced Time Section */}
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      marginBottom: "25px",
                      gap: "15px",
                    }}
                  >
                    <div style={{ width: "48%" }}>
                      <label
                        style={{
                          display: "block",
                          marginBottom: "8px",
                          fontSize: "16px",
                          textAlign: "left",
                          fontWeight: "500",
                          color: "#495057",
                        }}
                      >
                        Start Time *
                      </label>
                      <input
                        type="time"
                        list="startTimeOptions"
                        value={startTime}
                        onChange={(e) => setStartTime(e.target.value)}
                        style={{
                          width: "100%",
                          height: "45px",
                          borderRadius: "8px",
                          border: "2px solid #dee2e6",
                          paddingLeft: "15px",
                          fontSize: "16px",
                          outline: "none",
                        }}
                      />
                      <datalist id="startTimeOptions">
                        {timeOptions.map((option) => (
                          <option
                            key={option.value}
                            value={option.value}
                          ></option>
                        ))}
                      </datalist>
                    </div>
                    <div style={{ width: "48%" }}>
                      <label
                        style={{
                          display: "block",
                          marginBottom: "8px",
                          fontSize: "16px",
                          textAlign: "left",
                          fontWeight: "500",
                          color: "#495057",
                        }}
                      >
                        End Time *
                      </label>
                      <input
                        type="time"
                        list="endTimeOptions"
                        value={endTime}
                        onChange={(e) => setEndTime(e.target.value)}
                        style={{
                          width: "100%",
                          height: "45px",
                          borderRadius: "8px",
                          border: "2px solid #dee2e6",
                          paddingLeft: "15px",
                          fontSize: "16px",
                          outline: "none",
                        }}
                      />
                      <datalist id="endTimeOptions">
                        {timeOptions.map((option) => (
                          <option
                            key={option.value}
                            value={option.value}
                          ></option>
                        ))}
                      </datalist>
                    </div>
                  </div>

                  {/* Enhanced Button Section */}
                  <div
                    style={{
                      display: "flex",
                      gap: "15px",
                      justifyContent: "flex-end",
                      paddingTop: "15px",
                      borderTop: "2px solid #e9ecef",
                        position: "sticky",
                      bottom: "8px",
                      background:"#fff"
                    }}
                  >
                    <button
                      type="button"
                      onClick={handleModalClose}
                      style={{
                        backgroundColor: "#dc3545",
                        color: "white",
                        border: "none",
                        borderRadius: "8px",
                        padding: "12px 20px",
                        fontSize: "16px",
                        cursor: "pointer",
                        fontWeight: "500",
                        transition: "all 0.3s ease",
                      }}
                    >
                      Close
                    </button>
                    <button
                      type="submit"
                      disabled={waitForSubmission1}
                      style={{
                        backgroundColor: waitForSubmission1
                          ? "#6c757d"
                          : "#32406D",
                        color: "white",
                        border: "none",
                        borderRadius: "8px",
                        padding: "12px 24px",
                        fontSize: "16px",
                        cursor: waitForSubmission1 ? "not-allowed" : "pointer",
                        position: "relative",
                        fontWeight: "500",
                        minWidth: "100px",
                        transition: "all 0.3s ease",
                      }}
                    >
                      {waitForSubmission1 ? "" : "Schedule Meeting"}
                      <ThreeDots
                        wrapperClass="ovalSpinner"
                        wrapperStyle={{
                          position: "absolute",
                          left: "50%",
                          top: "50%",
                          transform: "translate(-50%, -50%)",
                        }}
                        visible={waitForSubmission1}
                        height="20"
                        width="20"
                        color="white"
                        ariaLabel="oval-loading"
                      />
                    </button>
                  </div>
                </div>
              </form>
            )}
          </>
        ) : (
          // Enhanced New Event Form
          <div
            style={{
              padding: "15px",
              background: "white",
              borderRadius: "12px",
              margin: "5px",
            }}
          >
            <div
              className="sheduhed"
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                position: "relative",
                width: "100%",
                marginBottom: "5px",
                paddingBottom: "5px",
                borderBottom: "2px solid #e9ecef",
              }}
            >
              <button
                type="button"
                onClick={handleBackToPlatformSelection}
                style={{
                  backgroundColor: "transparent",
                  border: "none",
                  color: "black",
                  cursor: "pointer",
                  fontSize: "30px",
                  padding: "1px",
                }}
              >
                ←
              </button>
              <div style={{ flex: 1, textAlign: "center" }}>
                <h3
                  style={{
                    marginBottom: "0",
                    fontSize: "20px",
                    color: "#32406D",
                    fontWeight: "600",
                  }}
                >
                  {meetingType === "call" ? "New Call " : "In Person"}  Event
                </h3>
              </div>

            </div>

            {/* Enhanced Event Form Fields */}
            <div style={{ marginBottom: "10px" }}>
              <label
                style={{
                  display: "block",
                  marginBottom: "6px",
                  fontSize: "16px",
                  fontWeight: "500",
                  color: "#495057",
                }}
              >
                Name *
              </label>
              <input
                type="text"
                value={eventName}
                onChange={(e) => setEventName(e.target.value)}
                placeholder="Enter event name"
                style={{
                  width: "100%",
                  height: "40px",
                  borderRadius: "8px",
                  border: "2px solid #dee2e6",
                  paddingLeft: "12px",
                  fontSize: "16px",
                  outline: "none",
                }}
              />
            </div>

            <div style={{ marginBottom: "10px" }}>
              <label
                style={{
                  display: "block",
                  marginBottom: "6px",
                  fontSize: "16px",
                  fontWeight: "500",
                  color: "#495057",
                }}
              >
                Purpose *
              </label>
              <input
                type="text"
                value={eventPurpose}
                onChange={(e) => setEventPurpose(e.target.value)}
                placeholder="Enter purpose"
                style={{
                  width: "100%",
                  height: "40px",
                  borderRadius: "8px",
                  border: "2px solid #dee2e6",
                  paddingLeft: "12px",
                  fontSize: "16px",
                  outline: "none",
                }}
              />
            </div>

            {/*  */}
            <div style={{ marginBottom: "10px" }}>
              <label
                style={{
                  display: "block",
                  marginBottom: "8px",
                  fontSize: "16px",
                  textAlign: "left",
                  fontWeight: "500",
                  color: "#495057",
                }}
              >
                Start Time *
              </label>
              <input
                type="time"
                list="startTimeOptions"
                value={startTime}
                onChange={(e) => setStartTime(e.target.value)}
                style={{
                  width: "100%",
                  height: "45px",
                  borderRadius: "8px",
                  border: "2px solid #dee2e6",
                  paddingLeft: "15px",
                  fontSize: "16px",
                  outline: "none",
                }}
              />
              <datalist id="startTimeOptions">
                {timeOptions.map((option) => (
                  <option
                    key={option.value}
                    value={option.value}
                  ></option>
                ))}
              </datalist>
            </div>
            {meetingType === "call" && (
              <div style={{ marginBottom: "10px" }}>
                <label
                  style={{
                    display: "block",
                    marginBottom: "6px",
                    fontSize: "16px",
                    fontWeight: "500",
                    color: "#495057",
                  }}
                >
                  Phone No
                </label>
                {/* <input
                type="number"
                placeholder="Enter mobile number"
                value={mobile}
                onChange={(e) => setMobile(e.target.value)}
                style={{
                  width: "100%",
                  height: "40px",
                  borderRadius: "8px",
                  border: "2px solid #dee2e6",
                  paddingLeft: "12px",
                  fontSize: "16px",
                  outline: "none",
                }}
              /> */}
                <PhoneInput
                  country={'in'} // No default country
                  value={mobile}
                  onChange={handleChange}
                  enableSearch={true}
                  inputStyle={{
                    width: '100%',
                    height: '40px',
                    borderRadius: '8px',
                    border: '2px solid #dee2e6',
                    fontSize: '16px',
                    paddingLeft: '40px',
                  }}
                  placeholder="Enter mobile number"
                />
                  {!isValid && <div style={{ color: 'red', marginTop: '5px' }}>{errorMsg}</div>}
              </div>
            )}
            {meetingType === "Inperson" && (
              <div style={{ marginBottom: "15px" }}>
                <label
                  style={{
                    display: "block",
                    marginBottom: "6px",
                    fontSize: "16px",
                    fontWeight: "500",
                    color: "#495057",
                  }}
                >
                  Email
                </label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter email"
                  style={{
                    width: "100%",
                    height: "40px",
                    borderRadius: "8px",
                    border: "2px solid #dee2e6",
                    paddingLeft: "12px",
                    fontSize: "16px",
                    outline: "none",
                  }}
                />
              </div>
            )}
              {meetingType === "Inperson" && (
                         <div style={{ marginBottom: "20px" }}>
                    <label
                      style={{
                        display: "block",
                        marginBottom: "8px",
                        fontSize: "16px",
                        textAlign: "left",
                        fontWeight: "500",
                        color: "#495057",
                      }}
                    >
                     Mail CC
                    </label>
                    <div ref={dropdownRef2} style={{ position: "relative" }}>
                      <div
                        style={{
                          display: "flex",
                          flexWrap: "nowrap",
                          alignItems: "center",
                          border: "2px solid #dee2e6",
                          borderRadius: "8px",
                          padding: "8px",
                          fontSize: "12px",
                          position: "relative",
                          overflowX: "auto",
                          minHeight: "45px",
                          background: "white",
                        }}
                      >
                        {selectedEmails2?.map((email, index) => (
                          <div
                            key={index}
                            style={{
                              display: "inline-flex",
                              alignItems: "center",
                              backgroundColor: "#484b4eff",
                              color: "white",
                              padding: "6px 12px",
                              borderRadius: "20px",
                              marginRight: "8px",
                              fontSize: "14px",
                              marginBottom: "0px"
                            }}
                          >
                            {email}
                            <span
                              onClick={() =>
                                handleEmailChange(email, "dropdown2")
                              }
                              style={{
                                marginLeft: "8px",
                                cursor: "pointer",
                                fontWeight: "bold",
                                fontSize: "16px",
                              }}
                            >
                              ×
                            </span>
                          </div>
                        ))}
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            flex: "1",
                          }}
                        >
                          <input
                            type="text"
                            placeholder="Search or add email"
                            onClick={() => handleDropdownClick("dropdown2")}
                            onChange={(e) => setSearchQuery2(e.target.value)}
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                handleEmailChange(e.target.value, "dropdown2");
                                setSearchQuery2("");
                              }
                            }}
                            value={searchQuery2}
                            style={{
                              flex: "1",
                              minWidth: "150px",
                              border: "none",
                              paddingLeft: "10px",
                              fontSize: "16px",
                              outline: "none",
                            }}
                            ref={inputRef2}
                          />
                          {searchQuery2 &&
                            ![...managers, ...recruiters].some((item) =>
                              item.email
                                .toLowerCase()
                                .includes(searchQuery2.toLowerCase())
                            ) && (
                              <button
                                onClick={handleAddEmailWithValidations}
                                style={{
                                  position: "absolute",
                                  right: "10px",
                                  backgroundColor: "#32406D",
                                  color: "white",
                                  border: "none",
                                  padding: "6px 12px",
                                  borderRadius: "6px",
                                  fontSize: "14px",
                                }}
                              >
                                Add
                              </button>
                            )}
                        </div>
                      </div>
                      {searchQuery2 && (
                        <div
                          className="dropdown-menu"
                          style={{
                            border: "2px solid #dee2e6",
                            borderRadius: "8px",
                            boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
                            marginTop: "5px",
                            width: "auto",
                            background: "white",
                            maxHeight: "200px",
                            overflowY: "auto",
                          }}
                        >
                          {[
                            ...new Set([
                              ...managers.map((item) =>
                                item.email.toLowerCase()
                              ),
                              ...recruiters.map((item) =>
                                item.email.toLowerCase()
                              ),
                            ]),
                          ]
                            .filter(
                              (email) =>
                                email.includes(searchQuery2.toLowerCase()) &&
                                email !== loggedInEmail
                            )
                            .map((email, index) => (
                              <label
                                key={index}
                                className="dropdown-item"
                                style={{
                                  display: "flex",
                                  padding: "12px",
                                  fontWeight: "normal",
                                  cursor: "pointer",
                                  borderBottom: "1px solid #f8f9fa",
                                  transition: "background-color 0.2s ease",
                                }}
                                onMouseEnter={(e) =>
                                  (e.target.style.backgroundColor = "#f8f9fa")
                                }
                                onMouseLeave={(e) =>
                                  (e.target.style.backgroundColor = "white")
                                }
                              >
                                <input
                                  type="checkbox"
                                  checked={selectedEmails2?.includes(email)}
                                  onChange={() =>
                                    handleEmailChange(email, "dropdown2")
                                  }
                                  style={{ marginRight: "12px" }}
                                />
                                {email}
                              </label>
                            ))}
                        </div>
                      )}
                    </div>
                  </div>
                            )}
            <div style={{ marginBottom: "10px" }}>
              <label
                style={{
                  display: "block",
                  marginBottom: "8px",
                  fontSize: "16px",
                  textAlign: "left",
                  fontWeight: "500",
                  color: "#000",
                }}
              >
               Date *
              </label>
              <input
                type="date"
                value={startDate}
                onChange={handleStartDateChange}
                style={{
                  width: "100%",
                  height: "45px",
                  borderRadius: "8px",
                  border: "2px solid #dee2e6",
                  paddingLeft: "15px",
                  fontSize: "16px",
                  outline: "none",
                }}
              />
            </div>

            {meetingType === "Inperson" && (
              <div style={{ marginBottom: "15px" }}>
                <label
                  style={{
                    display: "block",
                    marginBottom: "6px",
                    fontSize: "16px",
                    fontWeight: "500",
                    color: "#495057",
                  }}
                >
                  Location
                </label>
                <input
                  type="text"
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                  placeholder="Location"
                  style={{
                    width: "100%",
                    height: "40px",
                    borderRadius: "8px",
                    border: "2px solid #dee2e6",
                    paddingLeft: "12px",
                    fontSize: "16px",
                    outline: "none",
                  }}
                />
              </div>
            )}


            {/* Enhanced Event Buttons */}
            <div
              style={{
                display: "flex",
                gap: "15px",
                justifyContent: "flex-end",
                paddingTop: "15px",
                borderTop: "2px solid #e9ecef",
              }}
            >
              <button
                type="button"
                onClick={handleModalClose}
                style={{
                  backgroundColor: "#dc3545",
                  color: "white",
                  border: "none",
                  borderRadius: "8px",
                  padding: "12px 20px",
                  fontSize: "16px",
                  cursor: "pointer",
                  fontWeight: "500",
                }}
              >
                Close
              </button>
              <button
                type="submit"
                onClick={handleNewEventSubmit}
                disabled={waitForSubmission2}
                style={{
                  backgroundColor: waitForSubmission2 ? "#6c757d" : "#32406D",
                  color: "white",
                  border: "none",
                  borderRadius: "8px",
                  padding: "12px 24px",
                  fontSize: "16px",
                  cursor: waitForSubmission2 ? "not-allowed" : "pointer",
                  position: "relative",
                  fontWeight: "500",
                  minWidth: "100px",
                }}
              >
                {waitForSubmission2 ? "" : "Create Event"}
                <ThreeDots
                  wrapperClass="ovalSpinner"
                  wrapperStyle={{
                    position: "absolute",
                    left: "50%",
                    top: "50%",
                    transform: "translate(-50%, -50%)",
                  }}
                  visible={waitForSubmission2}
                  height="20"
                  width="20"
                  color="white"
                  ariaLabel="oval-loading"
                />
              </button>
            </div>
          </div>
        )}
      </Modal>

      {/* Success/Error Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        style={{
          overlay: {
            backgroundColor: "rgba(0, 0, 0, 0.75)",
            zIndex: 9999,
          },
          content: {
            color: "lightsteelblue",
            top: "50%",
            left: "50%",
            right: "auto",
            bottom: "auto",
            marginRight: "-50%",
            transform: "translate(-50%, -50%)",
            width: "350px",
            height: "150px",
            overflow: "hidden",
            borderRadius: "12px",
            background: "white",
          },
        }}
      >
        <div style={{ textAlign: "center", marginTop: "10px" }}>
          <p style={{ color: "#000", fontSize: "16px" }}>{modalMessage}</p>
        </div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            gap: "15px",
            width: "100%",
            marginTop: "30px",
          }}
        >
          <button
            onClick={() => {
              setIsModalOpen(false);
              // if (responseSuccess) {
              //   setShowCalendar(true);
              // }
            }}
            style={{
              color: "white",
              backgroundColor: "#28a745",
              border: "none",
              padding: "10px 20px",
              borderRadius: "6px",
              cursor: "pointer",
              fontSize: "14px",
              fontWeight: "500",
            }}
          >
            Ok
          </button>
          <button
            onClick={() => setIsModalOpen(false)}
            style={{
              color: "white",
              backgroundColor: "#dc3545",
              border: "none",
              padding: "10px 20px",
              borderRadius: "6px",
              cursor: "pointer",
              fontSize: "14px",
              fontWeight: "500",
            }}
          >
            Close
          </button>
        </div>
      </Modal>
    </div>
  );
};

export default ScheduleMeet;
