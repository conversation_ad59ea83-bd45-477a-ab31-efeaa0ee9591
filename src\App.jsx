import { useState, useEffect,useRef  } from "react";
import { BrowserRouter as Router, Route, Routes } from "react-router-dom";
import "./App.css";
import DashBoard from "./Views/Dashboard/DashBoard";
import RaiseIssue from "./Views/raiseissue";
import { useSelector, useDispatch } from "react-redux";
import PeerReview from "./Views/Dashboard/PeerReviews.jsx";
import "react-tooltip/dist/react-tooltip.css";
import AccountCreation from "./Views/AccountCreation/AccountCreation";
import RegisterCandidate from "./Views/RegisterCandidate/RegisterCandidate";
import AddCandidate from "./Views/AddCandidate";
import JobAssignments from "./Views/JobAssignment/JobAssignments";
import JobListing from "./Views/JobListing/JobListing";
import UserAccounts from "./Views/UserAccounts/UserAccounts";
import ChangePassword from "./Views/ChangePassword/ChangePassword";
import SubmissionSummary from "./Views/SubmissionSummary/SubmissionSummary";
import Tekspot_Curr_Opp from "./Views/TekspotCurrentOppurtunities/Tekspot_Curr_Opp";
import Assign_Tekspot_App from "./Views/AssignTekspotApplications/Assign_Tekspot_App";
import Tekspot_Applications from "./Views/TekspotApplications/Tekspot_Applications";
import NewReport from "./Views/NewReport.jsx";
import PeerAnalytics from "./Views/PeerReview/peeranalytics.jsx";
import ProfileTransfer from "./Views/ProfileTransfer/ProfileTransfer";
import Targetfix from "./Views/Targetfix.jsx";
import Otp from "./Views/otp/Otp.jsx";
import EditCandidate from "./Views/Dashboard/EditCandidate";
import Chatbotmain from "./Components/Chatbotmain.jsx";
import Login from "./Views/Login/login.jsx";
import Register from "./Views/Register/Register.jsx";
import Verify from "./Views/verify.jsx";
import Forgotpassword from "./Views/forgot password/Forgotpassword.jsx";
import EditJobStatus from "./Views/JobListing/EditJobStatus";
import ResumeFetch from "./Views/Resumefetch.jsx";
import AssignedRequirements from "./Views/AssignedRequirements/AssignedRequirements";
import Cookies from "universal-cookie";
import { useNavigate } from "react-router-dom";
import ReassignJob from "./Views/JobListing/ReassignJob";
import UpdateCandidate from "./Views/Dashboard/UpdateCandidate";
import EditJobPosting from "./Views/JobListing/EditJobPosting";
import OverView from "./Views/OverView.jsx";
import { toast } from "react-toastify";
import { setDashboardData } from "../src/store/slices/dashboardSlice.js";
import { setPeerReviewData } from "../src/store/slices/peerReviewSlice.js";
import { setAllJobs, updateSingleJob } from "../src/store/slices/jobSlice.js";
 
import {
  getDashboardData,
  getAllJobs,
  getAllRecruitersManagers,
    getCandidatesPendingPeerApproval,
} from "./Views/utilities.js";
 
const cookies = new Cookies();
 
const AuthGuard = ({ children }) => {
    const dispatch = useDispatch();
  const navigate = useNavigate(); // Must be inside Router context
 
const dashboardData = useSelector((state) => state.dashboardSliceReducer.dashboardData);
const dashboardDataRef = useRef(dashboardData);
const { candidatesPendingApproval, loading: peerReviewLoading, error: peerReviewError } = useSelector((state) => state.peerReviewSliceReducer);
const peerReviewDataRef = useRef(candidatesPendingApproval);
const { jobs } = useSelector((state) => state.jobSliceReducer);
const jobsDataRef = useRef(jobs);
   // Keep ref in sync with Redux
  //  console.log("peerReviewDataRef",peerReviewDataRef)
   useEffect(() => {
  dashboardDataRef.current = dashboardData;
}, [dashboardData]);
useEffect(() => {
  peerReviewDataRef.current = candidatesPendingApproval;
}, [candidatesPendingApproval]);

useEffect(() => {
  jobsDataRef.current = jobs;
}, [jobs]);
// Keep ref in sync with Redux
useEffect(() => {
  const userCookie = cookies.get("USERNAME");
  const userId = localStorage.getItem("user_id");
  const userName = localStorage.getItem("user_name");

  if (!userCookie) {
    navigate("/Login");
    return;
  }

  // Call APIs only once
  getDashboardData();
  getAllJobs();
  getAllRecruitersManagers();
  getCandidatesPendingPeerApproval();

  // Setup SSE connection
  const evtSource = new EventSource(
    ` http://127.0.0.1:5002/events?userid=${encodeURIComponent(userId)}&username=${encodeURIComponent(userName)}`
  );

  evtSource.onmessage = (event) => {
    const data = JSON.parse(event.data);
    console.log("SSE Update:", data);
    console.log("dashboardDataRef.current:", dashboardDataRef.current);

    // ✅ Handle candidate add/update/edit - FIXED: Added both event types
    if (
      data.event_type === "candidate_added" ||
      data.event_type === "candidate_updated" ||
      data.event_type === "candidate_editing"||
      // data.event_type === "peer_review_assigned"||
      data.event_type === "peer_review_status_updated"
    ) {
      const existingCandidates = dashboardDataRef.current?.candidates || [];
      const candidateIndex = existingCandidates.findIndex(
        (c) => c.id === data.id
      );

      let updatedCandidates;
      if (candidateIndex !== -1) {
        updatedCandidates = [...existingCandidates];
        updatedCandidates[candidateIndex] = data;

        toast.info(`Candidate "${data.name}" edited successfully`, {
          position: "top-right",
          autoClose: 3000,
          theme: "colored",
        });
      } else {
        updatedCandidates = [data, ...existingCandidates];

        toast.info(`New candidate "${data.name}" added successfully`, {
          position: "top-right",
          autoClose: 3000,
          theme: "colored",
        });
      }

      dispatch(
        setDashboardData({
          data: {
            ...dashboardDataRef.current,
            candidates: updatedCandidates,
          },
        })
      );

      // console.log("Dashboard Redux updated:", updatedCandidates);

    // ✅ Handle Peer Review Assigned
    } else if (data.event_type === "peer_review_assigned") {
      const existingCandidates =
        peerReviewDataRef.current || [];
      console.log("existingCandidates", existingCandidates, peerReviewDataRef);

      // Check if candidate already exists
      const candidateIndex = existingCandidates.findIndex(
        (c) => c.id === data.id
      );
      console.log("candidateIndex;", candidateIndex);

      let updatedCandidates;
      if (candidateIndex !== -1) {
        updatedCandidates = [...existingCandidates];
        updatedCandidates[candidateIndex] = data;
      } else {
        updatedCandidates = [data, ...existingCandidates];
      }

      console.log("updatedCandidates peer review", updatedCandidates);

      dispatch(
        setPeerReviewData({
          data: {
            ...peerReviewDataRef.current,
            candidates: updatedCandidates,
          },
        })
      );

      toast.info(`Peer review  "${data.name}" candidate assigned you once check it you 
        peer assigned Profile`, {
        position: "top-right",
        autoClose: 4000,
        theme: "colored",
      });

      console.log("Peer Review Redux updated:", updatedCandidates);

    // ✅ NEW: Handle Job Updates for JobListing page
    } else if (
      data.event_type === "job_created" ||
      data.event_type === "job_updated" ||
      data.event_type === "job_editing"
    ) {
      const existingJobs = jobsDataRef.current || [];
      const jobIndex = existingJobs.findIndex(
        (j) => j.id === data.id
      );

      let updatedJobs;
      if (jobIndex !== -1) {
        // Update existing job
        updatedJobs = [...existingJobs];
        updatedJobs[jobIndex] = { ...updatedJobs[jobIndex], ...data };

        toast.info(`Job "${data.role || data.title}" updated successfully`, {
          position: "top-right",
          autoClose: 3000,
          theme: "colored",
        });
      } else {
        // Add new job
        updatedJobs = [data, ...existingJobs];

        toast.info(`New job "${data.role}" added successfully`, {
          position: "top-right",
          autoClose: 3000,
          theme: "colored",
        });
      }

      dispatch(setAllJobs({ jobs: updatedJobs }));
      console.log("Jobs Redux updated:", updatedJobs);
    }
  };

  return () => {
    evtSource.close();
  };
}, [dispatch, navigate]);

  return children;
};
function App() {
 
  return (
    <Router>
     
      <Routes hashtype="noslash">
        <Route path="/" element={<Login />} />
        <Route path="/Login" element={<Login />} />
        <Route path="/Verify" element={<Verify />}/>
        <Route
          path="/DashBoard"
          element={
            <AuthGuard>
              <DashBoard />
            </AuthGuard>
          }
        />
          <Route
          path="/RaiseIssue"
          element={
            <AuthGuard>
              <RaiseIssue />
            </AuthGuard>
          }
        />
           <Route
          path="/PeerReview"
          element={
            <AuthGuard>
              <PeerReview />
            </AuthGuard>
          }
        />
        <Route
          path="/AccountCreation"
          element={
            <AuthGuard>
              <AccountCreation />
            </AuthGuard>
          }
        />
          <Route
          path="/Chatbotmain"
          element={
            <AuthGuard>
              <Chatbotmain />
            </AuthGuard>
          }
        />
   
        <Route
          path="/RegisterCandidate"
          element={
            <AuthGuard>
              <RegisterCandidate />
            </AuthGuard>
          }
        />
     
        <Route
          path="/UpdateCandidate"
          element={
            <AuthGuard>
              <UpdateCandidate />
            </AuthGuard>
          }
        />
        <Route
          path="/EditCandidate"
          element={
            <AuthGuard>
              <EditCandidate />
            </AuthGuard>
          }
        />
        <Route
          path="/ResumeFetch"
          element={
            <AuthGuard>
              <ResumeFetch />
            </AuthGuard>
          }
        />
        <Route
          path="/RegisterCandidate/AddCandidate"
          element={
            <AuthGuard>
              <AddCandidate />
            </AuthGuard>
          }
        />
        <Route
          path="/JobListing/AddCandidate"
          element={
            <AuthGuard>
              <AddCandidate />
            </AuthGuard>
          }
        />
        <Route
          path="/AssignedRequirements/AddCandidate"
          element={
            <AuthGuard>
              <AddCandidate />
            </AuthGuard>
          }
        />
        {/* <Route
          path={"/assign"}
          element={<Assign/>}
        /> */}
        <Route
          path="/JobAssignments"
          element={
            <AuthGuard>
              <JobAssignments />
            </AuthGuard>
          }
        />
        <Route
          path="/NewReport"
          element={
            <AuthGuard>
              <NewReport />
            </AuthGuard>
          }
        />
         <Route
          path="/PeerAnalytics"
          element={
            <AuthGuard>
              <PeerAnalytics />
            </AuthGuard>
          }
        />
          <Route
          path="/Targetfix"
          element={
            <AuthGuard>
              <Targetfix />
            </AuthGuard>
          }
        />
        <Route
          path="/JobListing"
          element={
            <AuthGuard>
              <JobListing />
            </AuthGuard>
          }
        />
        <Route
          path="/UserAccounts"
          element={
            <AuthGuard>
              <UserAccounts />
            </AuthGuard>
          }
        />
        {/* <Route
          path="/AccountDeactivation"
          element={
            <AuthGuard>
              <AccountDeactivation />
            </AuthGuard>
          }
        /> */}
        <Route
          path="/ChangePassword"
          element={
            <AuthGuard>
              <ChangePassword />
            </AuthGuard>
          }
        />
        <Route
          path="/SubmissionSummary"
          element={
            <AuthGuard>
              <SubmissionSummary />
            </AuthGuard>
          }
        />
          {/* <Route
          path="/Stoxxo"
          element={
            <AuthGuard>
              <Stoxxo />
            </AuthGuard>
          }
        /> */}
        <Route
          path="/Tekspot_Curr_Opp"
          element={
            <AuthGuard>
              <Tekspot_Curr_Opp />
            </AuthGuard>
          }
        />
        <Route
          path="/Assign_Tekspot_App"
          element={
            <AuthGuard>
              <Assign_Tekspot_App />
            </AuthGuard>
          }
        />
        <Route
          path="/Tekspot_Applications"
          element={
            <AuthGuard>
              <Tekspot_Applications />
            </AuthGuard>
          }
        />
        <Route
          path="/ProfileTransfer"
          element={
            <AuthGuard>
              <ProfileTransfer />
            </AuthGuard>
          }
        />
        <Route
          path="/AssignedRequirements"
          element={
            <AuthGuard>
              <AssignedRequirements />
            </AuthGuard>
          }
        />
        <Route
          path="/ReassignJob"
          element={
            <AuthGuard>
              <ReassignJob />
            </AuthGuard>
          }
        />
        <Route
          path="/EditJobStatus"
          element={
            <AuthGuard>
              <EditJobStatus />
            </AuthGuard>
          }
        />
        <Route path="/Otp" element={<Otp />} />
        <Route
          path="/EditJobPosting"
          element={
            <AuthGuard>
              <EditJobPosting />
            </AuthGuard>
          }
        />
     
       
 
        <Route path="/Forgotpassword" element={<Forgotpassword />} />
        <Route path="/Register" element={<Register />} />
        <Route
          path="/OverView"
          element={
            <AuthGuard>
              <OverView />
            </AuthGuard>
          }
        />
      </Routes>
    </Router>
  );
}
export default App;
 
 