
import React, { useState, useEffect, useRef, useMemo, forwardRef, useImperativeHandle, } from "react";
import { Link, useNavigate } from "react-router-dom";
import Modal from "react-modal";
import { useLocation } from "react-router-dom";
import "./leftnav.css";
import { toast } from "react-toastify";
import Cookies from "universal-cookie";
import <PERSON>ropper from "react-easy-crop";
import Slider from '@mui/material/Slider';
import { IoLocationOutline } from "react-icons/io5";
import { MdDelete } from "react-icons/md";
import { TailSpin } from "react-loader-spinner";
import { SiZoom } from "react-icons/si";
import ChatBotComponent from "./ChatBotComponent";
import { ThreeDots } from "react-loader-spinner";
import { MdCancel } from "react-icons/md";
import { saveAs } from 'file-saver';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import { useSelector } from 'react-redux';
import { useDispatch } from "react-redux";
import moment from 'moment';
import ScheduleMeet from "../Components/schedulemeet";
import { fetchMeetings } from "../Views/utilities";
import { IoIosPerson } from "react-icons/io";
import { clearCandidates } from "../store/slices/candidateSlice";
import 'react-big-calendar/lib/css/react-big-calendar.css';
import ResumeUpload from "../Components/ResumeUpload";
import { companyInfo } from "./companyinfo";
import { BsMicrosoftTeams } from "react-icons/bs";
import { IoCallSharp } from "react-icons/io5";
import "./chatbot.css"
import 'react-phone-input-2/lib/style.css';
import PhoneInput from 'react-phone-input-2';

const cookies = new Cookies();
Modal.setAppElement("#root");

const LeftNav = forwardRef(({ }, ref) => {
  const [chatMsgId, setChatMsgId] = useState(0);
  const localizer = momentLocalizer(moment);
  const [isOpen, setIsOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMessage, setModalMessage] = useState('');
  const [responseSuccess, setResponseSuccess] = useState(false);
  const [waitForSubmission1, setwaitForSubmission1] = useState(false);
  const [waitForSubmission2, setwaitForSubmission2] = useState(false);
  const [isActive, setIsActive] = useState(false);
  const [newEvents, setNewEvents] = useState([]);
  const [zoomMeetings, setZoomMeetings] = useState([]);
  const [showNewEvent, setShowNewEvent] = useState(false);
  useImperativeHandle(ref, () => ({
    open: () => {
      // console.log('Opening modal');
      setIsOpen(true);

    },
    close: () => {
      // console.log('Closing modal');
      setIsOpen(false);
    },

  }));

  const [showImageModal, setShowImageModal] = useState(false);  // State to handle modal visibility


  const handleImageClicks = () => {
    setShowImageModal(true);
  };

  const closeModals = () => {
    setShowImageModal(false);  // Close modal
  };



  const { events } = useSelector((state) => state.meetingSliceReducer);

  // console.log(events,"swsdvssdsdsfsfs")
  const formats = {
    timeGutterFormat: 'HH:mm',
    eventTimeRangeFormat: ({ start, end }, culture, localizer) =>
      `${localizer.format(start, 'HH:mm', culture)} - ${localizer.format(end, 'HH:mm', culture)}`,
    agendaTimeRangeFormat: ({ start, end }, culture, localizer) =>
      `${localizer.format(start, 'HH:mm', culture)} - ${localizer.format(end, 'HH:mm', culture)}`,
  };
  const { managers } = useSelector((state) => state.userSliceReducer);
  const dropdownRef = useRef(null);
  if (Array.isArray(managers)) {
    const emails = managers.map(manager => manager.email);
    // console.log(emails, "emails");
  } else {
    console.log("Managers is not an array or is empty");
  }
  const { recruiters } = useSelector((state) => state.userSliceReducer);
  if (Array.isArray(recruiters)) {
    const recruiteremails = recruiters.map(recruiters => recruiters.email);
    // console.log(recruiteremails, "recruiteremails");
  } else {
    console.log("recruiters is not an array or is empty");
  }


  const { dashboardData } = useSelector((state) => state.dashboardSliceReducer);
  const dispatch = useDispatch();
  const candidatesdata = dashboardData.candidates || [];

  const emails = candidatesdata.map(candidate => candidate.email).filter(email => email);
  const uniqueDataEmail = Array.from(new Set(emails));


  const [selectedEmails, setSelectedEmails] = useState([]);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const [isDropdownOpen1, setIsDropdownOpen1] = useState(false);
  const [selectedEmails1, setSelectedEmails1] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const inputRef = useRef(null);
  const [selectedEvent, setSelectedEvent] = useState({
    title: '',
    attendees: '',
    cc_recipients: '',
    description: '',
    start_date: '',
    end_date: '',
    time_zone: '',
    start_time: '',
    end_time: '',
    rec_email: '',
  });

  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.(com|in|net|org|co\.uk)$/;
  const isValidEmail = (email) => {
    return emailRegex.test(email);
  };

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
    setIsDropdownOpen1(true);
    setIsTyping(true);
  };


  const handleAddNewEmail = () => {
    const newEmail = searchQuery.trim();

    if (isValidEmail(newEmail)) {
      if (newEmail && !selectedEmails1.includes(newEmail)) {
        handleCheckboxChange(newEmail);
        setSelectedEmails1([...selectedEmails1, newEmail])
        setSearchQuery('');
        setIsDropdownOpen1(false);
      }
    } else {
      toast.error('Enter a valid email address.');
    }
  };




  const filteredEmails = uniqueDataEmail.filter(email =>
    email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  useEffect(() => {
    if (selectedEvent?.attendees) {
      let attendeesList = [];
      if (typeof selectedEvent.attendees === 'string') {
        attendeesList = selectedEvent.attendees.split(',').map(email => email.trim()).filter(email => email);
      } else if (Array.isArray(selectedEvent.attendees)) {
        attendeesList = selectedEvent.attendees.filter(email => email);
      }
      setSelectedEmails1(attendeesList);
    } else {
      setSelectedEmails1([]);
    }
  }, [selectedEvent]);



  const handleCheckboxChange = (email) => {
    setSelectedEmails1(prevSelectedEmails => {
      const newSelectedEmails = prevSelectedEmails.includes(email)
        ? prevSelectedEmails.filter(item => item !== email)
        : [...prevSelectedEmails, email];
      return newSelectedEmails;
    });
  };

  //PAVAN


  const [searchQuerys, setSearchQuerys] = useState(null);


  const toggleDropdown = () => {
    if (filteredManagers.length > 0) {
      setIsDropdownOpen(prev => !prev);
    }
  };


  const handleInputChange = (e) => {
    setSearchQuerys(e.target.value);
    if (!isDropdownOpen) {
      setIsDropdownOpen(true);
    }
  };

  const handleEmailChange1 = (email) => {
    setSelectedEmails(prevSelectedEmails => {
      const newSelectedEmails = prevSelectedEmails.includes(email)
        ? prevSelectedEmails.filter(item => item !== email)
        : [...prevSelectedEmails, email];
      setSearchQuerys('');
      return newSelectedEmails;
    });
  };

  const handleAddManualEmail = () => {
    if (searchQuerys && !selectedEmails.includes(searchQuerys)) {
      setSelectedEmails([...selectedEmails, searchQuerys]);
      setSearchQuerys('');
    }
  };



  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const loggedInEmail = localStorage.getItem('email').toLowerCase();

  // Filter managers and recruiters, excluding the logged-in user's email
  const filteredManagers = managers.filter(manager =>
    manager.email.toLowerCase().includes(searchQuerys?.toLowerCase()) &&
    manager.email.toLowerCase() !== loggedInEmail
  );

  const filteredRecruiters = recruiters.filter(recruiter =>
    recruiter.email.toLowerCase().includes(searchQuerys?.toLowerCase()) &&
    recruiter.email.toLowerCase() !== loggedInEmail
  );
  const selectedEmailsDisplay = selectedEmails.join(', ');
  const inputValues = selectedEmailsDisplay || searchQuerys;




  const handleCloseDropdown = (e) => {
    // Close the dropdown if clicked outside
    if (!e.target.closest('.dropdown')) {
      setIsDropdownOpen(false);
    }
  };

  // Add event listener to close dropdown when clicking outside
  React.useEffect(() => {
    document.addEventListener('click', handleCloseDropdown);
    return () => {
      document.removeEventListener('click', handleCloseDropdown);
    };
  }, []);
  const [EditModal, setEditModal] = useState(false);
  const EditopenModal = () => {
    setEditModal(true);
    setShowCalendar(false);
    setIsOpen(false);
    setShowmeet(false);
    //  setSelectedEvent(event);
    setIsInitialized(false); // 👈 Reset on modal open
    setFiles([]);
  }
  const EditcloseModal = () => {
    setEditModal(false);
    setSearchQuerys('');
    setFiles([])
    setSearchQuery('');
    setIsDropdownOpen1(false);
    setIsDropdownOpen(false);
    setIsInitialized(false);
    fetchMeetings()

  }
  const msgs = useMemo(() => {
    return ["Welcome to Makonis", "I am Jimmy", "How can i help you?"];
  }, []);
  const [i, setI] = useState(1);
  useEffect(() => {
    if (chatMsgId !== -1) {
      const intervalId = setInterval(() => {
        setChatMsgId(chatMsgId + 1);
      }, 2000)
      return () => clearInterval(intervalId);
    } else {
      if (chatMsgId === 3) {
        setChatMsgId(-1);
      }
    }
  }, [chatMsgId])

  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');
  const timeOptions = Array.from({ length: 24 * 2 }, (_, i) => {
    const hour = Math.floor(i / 2);
    const minute = (i % 2) * 30;
    return {
      value: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
      label: `${hour}:${minute.toString().padStart(2, '0')}`
    };
  });
  const handleStartTimeChange = (e) => {
    const newStartTime = e.target.value;
    setSelectedEvent(prevState => {
      // Calculate end time 30 minutes after the new start time
      const [hours, minutes] = newStartTime.split(':').map(Number);
      const startDate = new Date();
      startDate.setHours(hours);
      startDate.setMinutes(minutes);

      const endDate = new Date(startDate.getTime() + 30 * 60000); // Add 30 minutes

      const endHours = endDate.getHours().toString().padStart(2, '0');
      const endMinutes = endDate.getMinutes().toString().padStart(2, '0');
      const newEndTime = `${endHours}:${endMinutes}`;

      return {
        ...prevState,
        start_time: newStartTime,
        end_time: newEndTime,
      };
    });
  };
  const handleEndTimeChange = (e) => {
    const newEndTime = e.target.value;
    setSelectedEvent(prevState => ({
      ...prevState,
      end_time: newEndTime,
    }));
  };
  const handlecalleventStartTimeChange = (e) => {
    const newStartTime = e.target.value;

    setSelectedEvent(prevState => {
      const [hours, minutes] = newStartTime.split(':').map(Number);
      const startDate = new Date();
      startDate.setHours(hours);
      startDate.setMinutes(minutes);

      const endDate = new Date(startDate.getTime() + 30 * 60000);

      const endHours = endDate.getHours().toString().padStart(2, '0');
      const endMinutes = endDate.getMinutes().toString().padStart(2, '0');
      const newEndTime = `${endHours}:${endMinutes}`;

      return {
        ...prevState,
        time: newStartTime,       // ✅ match input value field
        end_time: newEndTime,
      };
    });
  };


  const add30Minutes = (time) => {
    const [hours, minutes] = time.split(':').map(Number);
    let newMinutes = minutes + 30;
    let newHours = hours;

    if (newMinutes >= 60) {
      newMinutes -= 60;
      newHours += 1;
    }

    if (newHours === 24) {
      newHours = 0;
    }

    return `${String(newHours).padStart(2, '0')}:${String(newMinutes).padStart(2, '0')}`;
  };

  useEffect(() => {
    if (startTime) {
      setEndTime(add30Minutes(startTime));
    } else {
      setEndTime('');
    }
  }, [startTime]);

  const [waitForSubmissiondel, setwaitForSubmissiondel] = useState(false);
  const handleDeletemeet = async (meeting_id, meeting_type, event_id) => {
    if (!waitForSubmissiondel) {
      setwaitForSubmissiondel(true);

      try {
        // Choose endpoint and payload based on meeting_type
        const isZoom = meeting_type === "Zoom";

        const endpoint = isZoom
          ? " http://127.0.0.1:5002/delete_zoom_meeting"
          : " http://127.0.0.1:5002/delete_event";

        const payload = isZoom
          ? { event_id } // For Zoom
          : { meeting_id }; // For Teams

        const response = await fetch(endpoint, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        });

        if (response.ok) {
          setShowmeet(false);
          setShowCalendar(false);
          setIsOpen(false);
          setwaitForSubmissiondel(false);
          toast.success("Meeting deleted successfully!");
          fetchMeetings(); // Refresh the calendar data
          fetchZoomMeetings
        } else {
          toast.error("Failed to delete meeting. Please try again.");
          setwaitForSubmissiondel(false);
        }

      } catch (err) {
        console.error(err);
        toast.error("Error occurred. Please try again.");
        setwaitForSubmissiondel(false);
      }
    }
  };

  const [waitForSubmissiondelevent, setwaitForSubmissiondelevent] = useState(false);
  // 👇 Move this outside useEffect so it's accessible anywhere
  const fetchCallEvents = async () => {
    const userId = localStorage.getItem("user_id");
    try {
      const response = await fetch(`  http://127.0.0.1:5002/get_all_call_events/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      const data = await response.json();
      const transformed = data.map((event) => {
        return {
          title: `${event.name} - ${event.purpose}`,
          start: event.date,
          end: event.date,
          id: event.id,
          mobile: event.phone,
          location: event.location,
          candidatereply: event.candidate_reply,
          reschedule: event.rescheduled_date,
          email: event.email,
          allDay: true,
          ...event,
        };
      });

      console.log("transformed:", transformed);
      setNewEvents(transformed);
    } catch (error) {
      console.error("Failed to fetch call events:", error);
    }
  };
  const fetchZoomMeetings = async () => {
    const userId = localStorage.getItem("user_id");
    try {
      const response = await fetch(" http://127.0.0.1:5002/get_zoom_meetings", {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      //  console.log(data.meetings,"sfdsdssdsfd")
      const transformedZoomMeetings = data.meetings.map((meeting) => {
        // const start = new Date(meeting.start_time);
        // const end = new Date(start.getTime() + meeting.duration * 60000); // add minutes
        const startDateTime = new Date(`${meeting.start_date}T${meeting.start_time}`);
        const endDateTime = new Date(`${meeting.end_date}T${meeting.end_time}`);
        return {
          title: `Zoom | ${meeting.subject}`,
          start: startDateTime,
          end: endDateTime,
          allDay: false,
          start_time: meeting.start_time,
          end_time: meeting.end_time,
          join_url: meeting.join_url,
          event_id: meeting.event_id,
          meeting_type: meeting.meeting_type,
          rec_email: meeting.recruiter_email,
          cc_recipients: meeting.cc_recipients,
          attendees: meeting.attendees,
          description: meeting.description,
          time_zone: meeting.time_zone,
          type: "zoom"
        };
      });
      console.log("transformedZoomMeetings", transformedZoomMeetings)
      setZoomMeetings(transformedZoomMeetings);
    } catch (error) {
      console.error("Failed to fetch call events:", error);
    }
  };

  // console.log(zoomMeetings,"zoomMeetings")
  const handleDeleteevent = async (meeting_id) => {
    if (!waitForSubmissiondelevent) {
      setwaitForSubmissiondelevent(true);

      try {
        const response = await fetch(
          `  http://127.0.0.1:5002/delete_call_event/${meeting_id}`, // Your endpoint
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },

          },
        );

        // Check if the response is OK
        if (response.ok) {
          const data = await response.json();
          setShowmeet(false)
          setShowCalendar(false)
          setIsOpen(false);
          //       // Show a success message
          setwaitForSubmissiondelevent(false);
          toast.success(data.message);
          fetchCallEvents()
          fetchZoomMeetings()
          // Update Redux state or UI here if needed
        } else {
          // Handle HTTP error responses
          toast.error("Failed to delete meeting. Please try again.");
          setwaitForSubmissiondelevent(false);
        }

      } catch (err) {
        console.error(err);
        setwaitForSubmissiondelevent(false);
        console.error("Error occurred, Please try again.");
      }
    }
  };

  const handleDownloadQuestions = () => {
    if (interviewData && interviewData.response && interviewData.response[0]) {
      const heading = interviewData.response[0].heading;
      let questions = [];

      if (general === "select all") {
        Object.keys(interviewData.response[0].questions).forEach(category => {
          questions.push(`<div style="font-weight: bold; color: black;">${category}</div>`);
          questions = questions.concat(interviewData.response[0].questions[category].map(cleanQuestion));
          questions.push("<br/>");
        });
      } else {
        questions.push(`<div style="font-weight: bold; color: green;">${general}</div>`);
        questions = questions.concat(interviewData.response[0].questions[general].map(cleanQuestion));
      }
      const content = `<html><body><div><span style="font-weight: bold; color: green;">${heading}</span></div><div>${questions.join('<br/>')}</div></body></html>`;
      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
      saveAs(blob, 'interview_questions.doc');
    }
  };


  const [notificationCount, setNoficationCount] = useState(0);
  const { pathname } = useLocation();
  const initial = {
    dashboard: false,
    registercandidate: false,
    joblisting: false,
    assignedrequirements: false,
  };
  const [show, setShow] = useState(initial);
  const [highlight, setHighlight] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [Telephonic, setTelephonic] = useState(false);
  const navigate = useNavigate();
  const USERTYPE = cookies.get("USERTYPE");
  // console.log("USERTYPE in loginpage", USERTYPE);
  const [userName, setUserName] = useState(cookies.get("USERNAME"));
  const [profileImage, setProfileImage] = useState(
    localStorage.getItem("profileImage") ||
    "https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_1280.png",
  );

  const [showOptions, setShowOptions] = useState(false);
  const [isCropping, setIsCropping] = useState(false);
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);
  const [imageForCropping, setImageForCropping] = useState(null);
  const [isPopupVisible, setIsPopupVisible] = useState(false);
  const timeoutDuration = 9000;
  const handleUserActivity = () => {
    if (!isPopupVisible) setI(1);
  };

  useEffect(() => {
    getCountOfNotifications();
    setToInitial()
  }, []);

  useEffect(() => {
    localStorage.getItem("profileImage");
  }, []);


  useEffect(() => {
    let lastActivityTime = Date.now();

    const checkTimeout = () => {
      const currentTime = Date.now();
      const elapsedTime = currentTime - lastActivityTime;

      if (elapsedTime > timeoutDuration * 1000) {
        setIsPopupVisible(true);
        handleConfirmLogout(2);
      }
    };

    const interval = setInterval(() => {
      checkTimeout();
    }, 1000);

    const handleUserActivity = () => {
      lastActivityTime = Date.now();
      // Additional logic if needed
    };

    window.addEventListener("mousemove", handleUserActivity);
    window.addEventListener("keypress", handleUserActivity);
    window.addEventListener("click", handleUserActivity);
    window.addEventListener("scroll", handleUserActivity);

    // Cleanup function to clear the interval and event listeners
    return () => {
      clearInterval(interval);
      window.removeEventListener("mousemove", handleUserActivity);
      window.removeEventListener("keypress", handleUserActivity);
      window.removeEventListener("click", handleUserActivity);
      window.removeEventListener("scroll", handleUserActivity);
    };
  }, []);
  const optionsRef = useRef(null);
  const imageContainerRef = useRef(null);

  useEffect(() => {
    setUserName(cookies.get("USERNAME"));

    fetchUserProfileImage();

  }, [localStorage.getItem("user_id")]);

  const fetchUserProfileImage = async () => {
    try {
      const response = await fetch(
        `  http://127.0.0.1:5002/user_image/${localStorage.getItem("user_id")}`,
        {
          method: "GET",
        },
      );

      if (!response.ok) {
        setProfileImage(
          "https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_1280.png",
        );
      } else {

        const blob = await response.blob();
        const imageUrl = await new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result);
          reader.onerror = () => reject(reader.error);
          reader.readAsDataURL(blob);
        });
        setProfileImage(imageUrl);

        localStorage.setItem("profileImage", imageUrl);
      }
    } catch (error) {
      console.error("Error fetching image:", error);
      localStorage.removeItem("profileImage");
      setProfileImage(
        "https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_1280.png",
      );
    }
  };


  useEffect(() => {

  }, [notificationCount]);

  const handleLogout = () => {
    setToInitial();
    setShowModal(true);
  };
  const [showCalendar, setShowCalendar] = useState(false);
  const handleCalendar = () => {

    fetchMeetings()
    fetchZoomMeetings()
    fetchCallEvents()
    setShowCalendar(true);
  };
  const closeModal = () => {
    setShowCalendar(false);
    setIsOpen(false)
  };

  const [showmeet, setShowmeet] = useState(false);

  const handlemeet = () => {
    setShowmeet(true);
  };
  const closemeet = () => {
    setShowmeet(false);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setTelephonic(false);
    setInterviewData(null);
    setgeneral("");

  };
  const handleOpenModal = () => {
    setTelephonic(true);
  };



  const handleConfirmLogout = async (identify) => {
    try {
      const response = await fetch("  http://127.0.0.1:5002/logout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          user_id: localStorage.getItem("user_id"),
        }),
      });

      if (response.ok) {
        dispatch(clearCandidates());
        const responselogout = await response.json();

        cookies.remove("USERNAME", { path: "/" });
        cookies.remove("USERTYPE", { path: "/" });
        localStorage.removeItem("profileImage");
        localStorage.removeItem("selectedProfile");
        localStorage.removeItem("selectedjobId");   // <-- this was missing
        localStorage.removeItem("selectedSkills");



        window.location.reload();
        if (localStorage.getItem("user_type")) {
          setToInitial();

          if (identify === 1) {
            navigate("/Login");
          } else {
            navigate("/Login", { state: { isPopupvisible: true } });
          }
        }
        setTimeout(() => {
          window.location.reload();
        }, 100);
      } else {
        console.error("Logout failed:", response.statusText);
      }
    } catch (error) {
      console.error("Error during logout:", error);
    }
  };

  const handleImageClick = () => {
    setShowOptions(!showOptions);
  };

  const handleUploadClick = (event) => {
    const fileInput = document.getElementById("fileInput");
    fileInput.addEventListener("change", handleFileChange);
    fileInput.click();
  };

  const handleFileChange = async (event) => {
    const file = event.target.files[0];

    if (file) {
      try {
        const imageUrl = URL.createObjectURL(file);
        setImageForCropping(imageUrl);
        setIsCropping(true);
      } catch (error) {
        console.error("Error reading file:", error);
      }
    }
  };

  const handleRemovePhoto = async () => {
    setShowOptions(false);

    try {
      const response = await fetch(
        `  http://127.0.0.1:5002/delete_user_image/${localStorage.getItem("user_id")}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            // Send the profile image URL to the server for deletion
            profileImage: localStorage.getItem("profileImage"),
            image_delete_status: true,
          }),
        },
      );

      if (!response.ok) {
        throw new Error("Failed to remove image");
      }

      const result = await response.json();
      if (localStorage.getItem("profileImage")) {
        // Remove the profile image URL from local storage
        localStorage.removeItem("profileImage");
      }

      // Remove the profile image URL from local storage
      fetchUserProfileImage();

      // Set the profile image state to null to update the UI
      setProfileImage(null);
    } catch (error) {
      console.error("Error removing image:", error);
    }
  };

  const handleClickOutside = (event) => {
    if (
      optionsRef.current &&
      !optionsRef.current.contains(event.target) &&
      imageContainerRef.current &&
      !imageContainerRef.current.contains(event.target)
    ) {
      setShowOptions(false);
    }
  };

  useEffect(() => {
    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, [showOptions]);

  useEffect(() => {
    setShow(initial);
  }, []);

  useEffect(() => {

    if (localStorage.getItem("path")) {
      const parent = localStorage
        .getItem("path")
        ?.trim()
        .substring(1)
        .toLowerCase();

      setShow({
        ...initial,
        [parent]: true,
      });

    } else {
      console.log("else case");
    }
  }, [localStorage.getItem("path")]);

  useEffect(() => {
    console.log(show);
  }, []);
  const capitalizeFirstLetter = (string) => {
    return string.charAt(0).toUpperCase() + string.slice(1);
  };

  const getCountOfNotifications = async () => {

    const id = localStorage.getItem("user_id");
    try {
      const response = await fetch(
        `  http://127.0.0.1:5002/jobs_notification/${id}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      if (response.ok) {
        const data = await response.json();

        let sum = 0;
        for (let j = 0; j < data?.length; j++) {
          sum += data[j].num_notification;
        }
        setNoficationCount(sum);
      } else {
        console.log(response.statusText);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const updateNotificationCount = async () => {
    const id = localStorage.getItem("user_id");
    try {
      const response = await fetch(
        `  http://127.0.0.1:5002/checked_jobs_notification/${id}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            checked_notification_status: true,
          }),
        },
      );
      if (response.ok) {

        const data = response.json();
        await getCountOfNotifications();

      } else {

      }
    } catch (err) {
      console.log("handle error", err);
    }
  };


  const assignRequirementClicked = async () => {

    setToInitial();

    await updateNotificationCount();
  };

  const userNameCapitalized = userName ? capitalizeFirstLetter(userName) : "";

  const createImage = (url) =>
    new Promise((resolve, reject) => {
      const image = new Image();
      image.addEventListener("load", () => resolve(image));
      image.addEventListener("error", (error) => reject(error));
      image.setAttribute("crossOrigin", "anonymous");
      image.src = url;
    });

  const getCroppedImg = async (imageSrc, pixelCrop) => {
    const image = await createImage(imageSrc);
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");

    canvas.width = pixelCrop.width;
    canvas.height = pixelCrop.height;

    ctx.drawImage(
      image,
      pixelCrop.x,
      pixelCrop.y,
      pixelCrop.width,
      pixelCrop.height,
      0,
      0,
      pixelCrop.width,
      pixelCrop.height,
    );

    return new Promise((resolve, reject) => {
      canvas.toBlob((blob) => {
        if (!blob) {
          reject(new Error("Canvas is empty"));
          return;
        }
        blob.name = "croppedImage.jpeg";
        resolve(blob);
      }, "image/jpeg");
    });
  };

  const handleSaveCroppedImage = async () => {
    try {
      const croppedImageBlob = await getCroppedImg(
        imageForCropping,
        croppedAreaPixels,
      );
      // console.log("Cropped Image Blob:", croppedImageBlob);
      const formData = new FormData();
      formData.append("image", croppedImageBlob, "croppedImage.jpeg");

      const reader = new FileReader();
      reader.readAsDataURL(croppedImageBlob);
      reader.onloadend = async () => {
        const base64String = reader.result.replace(
          /^data:image\/(png|jpeg);base64,/,
          "",
        );
        await uploadCroppedImage(base64String, "croppedImage.jpeg");
      };
    } catch (error) {
      console.error("Error saving cropped image:", error.message);
    }
  };

  const uploadCroppedImage = async (base64Image, filename) => {
    try {
      // console.log("Base64 Image String:", typeof base64Image);

      const response = await fetch(
        `  http://127.0.0.1:5002/upload_user_image/${localStorage.getItem("user_id")}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            image: base64Image,
            filename,
            image_delete_status: false,
          }),
        },
      );

      if (!response.ok) {
        throw new Error("Failed to upload image");
      }

      // console.log("Response Status Text:", response.statusText);

      const result = await response.json();
      // console.log("Upload Result:", result);
      fetchUserProfileImage();
      const newImageUrl = result.imageUrl; // Assuming the server returns the image URL
      setProfileImage(newImageUrl);
      localStorage.setItem("profileImage", newImageUrl);
    } catch (error) {
      console.error("Error uploading cropped image:", error);
    } finally {
      setIsCropping(false);
      setShowOptions(false);
    }
  };
  const fileToBase64 = (file) => {
    if (file === null) return;
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        resolve(reader.result.split(",")[1]);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
      // console.log(file, "pdf")
    });
  };
  const handleresumeChange = (e) => {
    setSelectedFile(e.target.files[0]);
  };
  const [interviewData, setInterviewData] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [selectedOption, setSelectedOption] = useState('');
  const [description, setDescription] = useState('');
  const handleDescriptionChange = (e) => {
    setDescription(e.target.value);
  };
  const [general, setgeneral] = useState("")

  //console.log(events,"inleftnav")

  const transformEvents = (events) => {
    return events?.map(event => {
      const { title, start_date, description, end_date, start_time, end_time, join_url, meeting_id, time_zone, cc_recipients, attendees, rec_email, files, meeting_type } = event;
      // Combine date and time into Date objects
      const startDateTime = new Date(`${start_date}T${start_time}`);
      const endDateTime = new Date(`${end_date}T${end_time}`);
      return {
        title,
        start: startDateTime,
        end: endDateTime,
        description,
        join_url,
        meeting_id,
        start_time,
        meeting_type,
        end_time,
        attendees,
        cc_recipients,
        time_zone,
        rec_email,
        files
      };
    });
  };


  const cleanQuestion = (question) => {
    return question.replace(/[*#]+/g, '').trim();
  };
  const handleSelectChange = (e) => {
    setSelectedOption(e.target.value);
  };
  const handlegeneralchange = (e) => {
    setgeneral(e.target.value)
  }
  const [waitForSubmission, setWaitForSubmission] = useState(false);
  const [selectAllData, setSelectAllData] = useState([]);
  const [indexes, setIndexes] = useState([]);
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setWaitForSubmission(true)
      const base64String = await fileToBase64(selectedFile);
      // console.log("Base64 String:");
      const body_data = {
        user_id: localStorage.getItem("user_id"),
        recruiter_prompt: selectedOption,
        resume: base64String

      };


      const response = await fetch(
        "  http://127.0.0.1:5002/generate_questions", {

        method: "POST",

        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body_data),
      });

      const data = await response.json();
      setWaitForSubmission(false);
      if (!data.response || !data.response[0] || data.response[0].questions === undefined || Object.keys(data.response[0].questions).length === 0) {
        setInnerModal(true);
      }
      // console.log(data.response[0]?.questions)
      let tempList = []
      let tempIndexes = []
      const selectAllKeys = Object.keys(data.response[0]?.questions);
      for (let i = 0; i < Object.keys(data.response[0].questions).length; i++) {
        tempIndexes.push(tempList.length);
        tempList.push(selectAllKeys[i])
        const tempArr = data.response[0].questions[selectAllKeys[i]]
        console.log(tempArr)
        tempList = tempList.concat(tempArr)
      }
      // console.log(tempList)
      setIndexes(tempIndexes)
      setSelectAllData(tempList)
      setInterviewData(data);
      // console.log("data", data);

    } catch (err) {
      // console.log("handle error", err);
    }

  };
  const [files, setFiles] = useState([]);
  const [existingFiles, setExistingFiles] = useState([]);



  const [isInitialized, setIsInitialized] = useState(false);
  useEffect(() => {
    if (selectedEvent && !isInitialized) {
      if (typeof selectedEvent.files === "string") {
        try {
          const parsed = JSON.parse(selectedEvent.files);
          setExistingFiles(parsed);
        } catch (e) {
          console.error("Failed to parse existing files", e);
        }
      } else if (Array.isArray(selectedEvent.files)) {
        setExistingFiles(selectedEvent.files);
      }
      setIsInitialized(true);
    }
  }, [selectedEvent, isInitialized]);



  const eventfileToBase64 = (file) => new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const result = reader.result;
      const base64 = result.split(',')[1];
      resolve(base64);
    };
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });

  const handleFileChangeevent = (e) => {
    const selected = Array.from(e.target.files);
    setFiles((prev) => [...prev, ...selected]);
  };


  const handleRemoveFile = (index) => {
    setFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const handleRemoveExistingFile = (index) => {
    setExistingFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const handleFormSubmit = async (e) => {
    e.preventDefault(); // Prevent default form submission

    if (!selectedEvent.title || !startDateValue || !endDateValue || !selectedEvent.start_time || !selectedEvent.end_time || !selectedEvent.time_zone || selectedEmails1.length === 0) {
      toast.error('Please fill in all required fields.');
      return; // Prevent further execution
    }
    if (!mobile || mobile.length < 8) {
      alert("Please enter a valid phone number.");
      return;
    }
    const filePayload = await Promise.all(
      files.map(async (file) => ({
        file_name: file.name,
        file_content_base64: await eventfileToBase64(file),
      }))
    );

    const isZoomMeeting = selectedEvent?.meeting_type === 'Zoom';

    const payload = {
      subject: selectedEvent.title,
      attendees: selectedEmails1,
      cc_recipients: selectedEmails,
      description: selectedEvent.description,
      start_date: startDateValue,
      end_date: endDateValue,
      time_zone: selectedEvent.time_zone,
      start_time: isZoomMeeting ? selectedEvent.start_time.slice(0, 5) : selectedEvent.start_time,
      end_time: isZoomMeeting ? selectedEvent.end_time.slice(0, 5) : selectedEvent.end_time,
      recruiter_email: localStorage.getItem("email"),
      recruiter_id: localStorage.getItem("user_id"),
      rec_email: selectedEvent.email,
      existing_files: existingFiles.length > 0 ? existingFiles : null,
      new_files: filePayload,
    };
    console.log(selectedEvent?.meeting_type === 'Zoom', "payload");
    console.log(selectedEvent.event_id, "payload");
    let endpoint = " http://127.0.0.1:5002/update_event"; // Default for Teams
    if (selectedEvent?.meeting_type === 'Zoom') {
      endpoint = " http://127.0.0.1:5002/edit_zoom_meeting";
      payload.event_id = selectedEvent.event_id; // Add 'event_id' for Zoom
    } else {
      payload.meeting_id = selectedEvent.meeting_id; // Add 'meeting_id' for Teams
    }
    if (!waitForSubmission1) {
      setwaitForSubmission1(true);
      setResponseSuccess(false);

      try {
        // Send the request
        const response = await fetch(endpoint, {
          method: 'POST', // Method should be 'POST' if you're creating or updating the resource
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        });

        if (!response.ok) {
          throw new Error('Network response was not ok');
          setwaitForSubmission1(false);
        }

        const result = await response.json();
        // console.log('Success:', result);
        setwaitForSubmission1(false);
        EditcloseModal();
        fetchMeetings();
        setModalMessage('Meeting edited successfully');
        setResponseSuccess(true);
        setIsModalOpen(true);
        // await syncEvents();
        fetchMeetings();
      } catch (error) {
        console.error('Error:', error);
        setwaitForSubmission1(false);
        setModalMessage('An error occurred. Please try again.');
        setIsModalOpen(true);
      }
    }
  };

  
  const handleEventcallSubmit = async (e) => {
    e.preventDefault(); // Prevent default form submission

    if (waitForSubmission2) return; // Avoid multiple submissions

    setwaitForSubmission2(true);

    let endpoint = '';
    let payload = {};

    if (selectedEvent.meeting_type === 'Call') {
      endpoint = ` http://127.0.0.1:5002/
 /edit_call_event/${selectedEvent.id}`;
      payload = {
        name: selectedEvent.name,
        purpose: selectedEvent.purpose,
        // id:selectedEvent.id,
        time: selectedEvent.time, // Corresponds to starttime
        date: selectedEvent.end,      // Corresponds to date
        mobile: selectedEvent.mobile,             // Corresponds to phone no
      };
    } else if (selectedEvent.meeting_type === 'In-person') {
      endpoint = ` http://127.0.0.1:5002/
 /edit_in_person_event/${selectedEvent.id}`;
      payload = {
        name: selectedEvent.name,
        purpose: selectedEvent.purpose,
        // id:selectedEvent.id,
        time: selectedEvent.time, // Corresponds to starttime
        email: selectedEvent.email, // Corresponds to email
        date: selectedEvent.end,      // Corresponds to date
        location: selectedEvent.location,           // Corresponds to location
      };
    } else {
      toast.error('Invalid event type.');
      setwaitForSubmission2(false);
      return;
    }

    try {
      const response = await fetch(endpoint, {
        method: 'POST', // Note: For editing, 'PUT' or 'PATCH' are often more semantically correct
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        // Throw an error to be caught by the catch block
        throw new Error('Network response was not ok');
      }

      await response.json(); // Wait for the JSON parsing

      toast.success('Event edited successfully');
      EditcloseModal();

    } catch (error) {
      console.error('Error submitting event:', error);
      toast.error('An error occurred. Please try again.');
    } finally {
      // This block runs whether the try succeeded or failed
      setwaitForSubmission2(false);
    }
  };

  const handleCropComplete = (croppedArea, croppedAreaPixels) => {
    setCroppedAreaPixels(croppedAreaPixels);
  };
  const setToInitial = () => {

    localStorage.removeItem("path");
  };
  const [innerModal, setInnerModal] = useState(false);

  const handleCloseInnerModal = () => {
    setInnerModal(false);
  }
  const handleMouseEnter = () => {
    setShowOptions(true);
  };

  const handleMouseLeave = () => {
    setShowOptions(false);
  };
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const toggleSidebar = () => {
    // Check if the current window width is 542px or less
    if (window.innerWidth <= 542) {
      setSidebarOpen(!sidebarOpen);

      if (sidebarOpen) {
        document.querySelector("body").classList.remove("active");
        document.querySelector("body").classList.add("barside2");
      } else {
        document.querySelector("body").classList.add("active");
        document.querySelector("body").classList.add("closebar");
      }
    }
  };

  const handleEventClick = (event) => {

    setSelectedEvent(event);

    setShowmeet(true); // This will open the handlemeet modal
  };

  useEffect(() => {
    if (typeof selectedEvent?.attendees === 'string') {
      const emails = selectedEvent.attendees.split(',').map(email => email.trim());
      setSelectedEmails1(emails);
    }
  }, [selectedEvent?.attendees]);

  useEffect(() => {
    if (typeof selectedEvent?.cc_recipients === 'string') {
      const emails = selectedEvent.cc_recipients.split(',').map(email => email.trim());
      setSelectedEmails(emails);
    }
  }, [selectedEvent?.cc_recipients]);

  const startDate = new Date(selectedEvent?.start);
  const endDate = new Date(selectedEvent?.end);

  const formattedStartDate = startDate.toLocaleDateString('en-US', {
    weekday: 'long', // Full day name
    month: 'long',   // Full month name
    day: 'numeric'   // Day of the month
  });

  const formattedStartTime = startDate.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false    // 24-hour format
  });

  const formattedEndTime = endDate.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false    // 24-hour format
  });

  const parseDate = (dateValue) => {
    const parsedDate = new Date(dateValue);
    return isNaN(parsedDate.getTime()) ? new Date() : parsedDate;
  };

  // Ensure default values in case selectedEvent or dates are undefined
  const [endDateManuallyChanged, setEndDateManuallyChanged] = useState(false);

  const startDates = parseDate(selectedEvent?.start);
  const endDates = parseDate(selectedEvent?.end);

  const startDateValue = startDates.toISOString().split('T')[0];
  const endDateValue = endDates.toISOString().split('T')[0];


  const handleStartDateChange = (e) => {    // new functionality
    const newStartDate = new Date(e.target.value);
    if (!isNaN(newStartDate.getTime())) {
      setSelectedEvent((prev) => {
        // If user hasn't modified end date manually, sync end date with start date
        const newEndDate = endDateManuallyChanged ? prev.end : newStartDate;
        return {
          ...prev,
          start: newStartDate,
          end: newEndDate,
        };
      });
    }
  };

  const handleEndDateChange = (e) => {   // new functionality
    const newEndDate = new Date(e.target.value);
    if (!isNaN(newEndDate.getTime())) {
      setSelectedEvent((prev) => ({
        ...prev,
        end: newEndDate,
      }));
      setEndDateManuallyChanged(true); // Mark end date as manually changed
    }

    if (new Date(startDates) > newEndDate) {
      seterror('End Date is Earlier than Start Date.');
      toast.error('End Date is Earlier than Start Date.');
    } else {
      seterror('');
    }
  };




  const [interviewModal, setInterviewModal] = useState(false);

  const InterviewcloseModal = () => {
    setInterviewModal(false);
    setShowCalendar(false);
    // resetForm();

  };

  const [start_autoDate, setStartautoDate] = useState("");
  const [end_autoDate, setEndautoDate] = useState("");
  const [startautoTime, setStartautoTime] = useState("");
  const [endautoTime, setEndautoTime] = useState("");

  const handleEmptySlotClick = (slotInfo) => {
    // Open the modal here
    setInterviewModal(true);
    setShowCalendar(false);

    // Optionally, set start and end times based on the slot clicked
    setStartautoDate(slotInfo.start.toLocaleDateString('en-CA'));
    setEndautoDate(slotInfo.end.toLocaleDateString('en-CA'));
    setStartautoTime(slotInfo.start.toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit' }));
    setEndautoTime(slotInfo.end.toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit' }));

  };
  // console.log(start_autoDate, "start_autoDate check in left nav page");
  const formatTimeTo24Hour = (timeString) => {
    const [time, modifier] = timeString.split(' ');
    let [hours, minutes] = time.split(':');

    if (modifier === 'PM' && hours !== '12') {
      hours = parseInt(hours, 10) + 12;
    }
    if (modifier === 'AM' && hours === '12') {
      hours = '00';
    }

    return `${hours.padStart(2, '0')}:${minutes}`;
  };
  useEffect(() => {
    if (selectedEvent && selectedEvent.start_time && selectedEvent.end_time) {
      const formattedStartTime = formatTimeTo24Hour(selectedEvent.start_time);
      const formattedEndTime = formatTimeTo24Hour(selectedEvent.end_time);
      setStartTime(formattedStartTime);
      setEndTime(formattedEndTime);
    }
  }, [selectedEvent]);
  const [error, seterror] = useState('');



  const [ResumeModal, setResumeModal] = useState(false);

  const handleResumeUpload = () => {
    setResumeModal(true)
  }
  const handleCloseResume = () => {
    setResumeModal(false)
  }
  const [selectedOptions, setSelectedOptions] = useState("Job_Description");

  const [jdselectedFile, setjdSelectedFile] = useState(null);
  const handleJobdescriptionchange = (e) => {
    setjdSelectedFile(e.target.files[0]);
  };
  const [jdQuestions, setJdQuestions] = useState([]);
  const handleSubmitJD = async (e) => {
    e.preventDefault();
    const jdText = document.getElementById("jdText")?.value;
    try {
      setWaitForSubmission(true);
      const base64String = await fileToBase64(selectedFile);
      const jobDescriptionBase64 = await fileToBase64(jdselectedFile);
      // console.log("Base64 String:");
      let body_data = {
        user_id: localStorage.getItem("user_id"),
        resume: base64String,
      };
      if (jobDescriptionBase64) {
        body_data.job_description_base64 = jobDescriptionBase64;
        body_data.job_description_text = null;
      } else if (jdText) {
        body_data.job_description_text = jdText;
        body_data.job_description_base64 = null;
      }

      // console.log("Job Post Request Body:", body_data);

      const response = await fetch(
        "  http://127.0.0.1:5002/generate_questions_jd",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(body_data),
        }
      );

      if (response.ok) {
        const data = await response.json();
        setWaitForSubmission(false);

        if (data.jd_questions) {
          setJdQuestions(data.jd_questions);
        }
      } else {
        console.log("Response not OK:", response.status, response.statusText);
        setWaitForSubmission(false);
      }
    } catch (err) {
      console.log("handle error", err);
      setWaitForSubmission(false);
    }
  };


  const [selectedDomain, setSelectedDomain] = useState(''); // To track selected domain
  const [filteredQuestions, setFilteredQuestions] = useState([]);

  // console.log("jdfrom bot",jdQuestions.length)
  const handleDomainChange = (event) => {
    const domain = event.target.value;
    setSelectedDomain(domain);

    if (domain === "all") {
      // Combine all questions across all domains
      const allQuestionsWithDomain = Object.entries(jdQuestions).map(
        ([domain, questions]) => ({
          domain,
          questions,
        })
      );
      setFilteredQuestions(allQuestionsWithDomain);
    } else if (domain) {
      // Filter questions for the selected domain
      const questions = jdQuestions[domain];
      if (questions) {
        setFilteredQuestions([{ domain, questions }]);
      } else {
        setFilteredQuestions([]);
      }
    } else {
      setFilteredQuestions([]);
    }
  };

  //  ere start chat bots
  const chatBodyRef = useRef();
  const [showChatbot, setShowChatbot] = useState(false);
  const [chatHistory, setChatHistory] = useState([
    {
      hideInChat: true,
      role: "model",
      text: companyInfo,
    },
  ]);
  const generateBotResponse = async (history) => {
    // Helper function to update chat history
    const updateHistory = (text, isError = false) => {
      setChatHistory((prev) => [...prev.filter((msg) => msg.text != "Thinking..."), { role: "model", text, isError }]);
    };
    // Format chat history for API request
    history = history.map(({ role, text }) => ({ role, parts: [{ text }] }));
    const requestOptions = {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ contents: history }),
    };
    try {
      // Make the API call to get the bot's response
      const response = await fetch(import.meta.env.VITE_API_URL, requestOptions);
      const data = await response.json();
      if (!response.ok) throw new Error(data?.error.message || "Something went wrong!");
      // Clean and update chat history with bot's response
      const apiResponseText = data.candidatesdata[0].content.parts[0].text.replace(/\*\*(.*?)\*\*/g, "$1").trim();
      updateHistory(apiResponseText);
    } catch (error) {
      // Update chat history with the error message
      updateHistory(error.message, true);
    }
  };

  // new events 
  const transformedEvents = transformEvents(events);
  //  console.log("newEvents",newEvents)
  //  console.log("zoomEvents",zoomMeetings)
  const allEvents = [...transformedEvents, ...newEvents, ...zoomMeetings];
  //  console.log(allEvents, "transformedEvents");
  const isCallEvent = selectedEvent && selectedEvent.datecallevent && selectedEvent.purpose && selectedEvent.mobile && selectedEvent.time && selectedEvent.mobile && !selectedEvent.id;

  // // Logout styles 
  // console.log(selectedEvent?.meeting_type, "selectedEvent.meeting_type");
  const AlertIcon = () => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="#DC2626" // Red color for the icon stroke
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
      <line x1="12" y1="9" x2="12" y2="13"></line>
      <line x1="12" y1="17" x2="12.01" y2="17"></line>
    </svg>
  );
  const modalStyles = {
    overlay: {
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      zIndex: 1000,
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
    },
    content: {
      position: "relative",
      top: "auto",
      left: "auto",
      right: "auto",
      bottom: "auto",
      maxWidth: "420px",
      width: "90%",
      background: "#fff",
      borderRadius: "12px",
      padding: "24px",
      border: "none",
      boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      textAlign: "center",
    },
  };

  const styles = {
    openButton: {
      padding: "10px 20px",
      fontSize: "16px",
      cursor: "pointer",
    },
    iconContainer: {
      width: "48px",
      height: "48px",
      borderRadius: "50%",
      backgroundColor: "#FEE2E2", // Light red background for the icon
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      marginBottom: "16px",
    },
    title: {
      fontSize: "20px",
      fontWeight: "600",
      color: "#111827", // Dark gray
      margin: "0 0 8px 0",
    },
    subtitle: {
      fontSize: "15px",
      color: "#6B7280", // Medium gray
      margin: "0 0 24px 0",
      lineHeight: 1.5,
    },
    buttonContainer: {
      display: "flex",
      gap: "12px",
      width: "100%",
    },
    cancelButton: {
      flex: 1, // Makes buttons share width equally
      padding: "10px",
      fontSize: "16px",
      fontWeight: "500",
      color: "#374151",
      backgroundColor: "#fff",
      border: "1px solid #D1D5DB", // Light gray border
      borderRadius: "8px",
      cursor: "pointer",
    },
    logoutButton: {
      flex: 1,
      padding: "10px",
      fontSize: "16px",
      fontWeight: "500",
      color: "#fff",
      backgroundColor: "#DC2626", // Red
      border: "none",
      borderRadius: "8px",
      cursor: "pointer",
    },
  };

  const isCallOrInPersonEvent =
    selectedEvent?.meeting_type === 'Call' ||
    selectedEvent?.meeting_type === 'In-person';
  return (
    <div>
      <div className="sidebar">
        <div className="profile">
          <div
            className="image-container"
            ref={imageContainerRef}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            style={{ position: "relative", cursor: "pointer" }}
            onClick={handleImageClicks}
          >
            <img src={profileImage} alt="" />
            {showOptions && (
              <div
                className="options"
                ref={optionsRef}
                style={{
                  position: "absolute",
                  top: "100%",
                  left: "50%",
                  transform: "translateX(-50%)",
                  backgroundColor: "#fff",
                  boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
                  borderRadius: "4px",
                  zIndex: 100,
                }}
              >
                <div
                  className="img_u"
                  onClick={handleUploadClick}
                  style={{
                    padding: "2px",
                    cursor: "pointer",
                    fontSize: "14px",
                    fontFamily: "poppins,sanserif",
                  }}
                >
                  Upload Photo
                </div>
                <div
                  className="img_u"
                  onClick={handleRemovePhoto}
                  style={{
                    padding: "3px",
                    cursor: "pointer",
                    fontSize: "14px",
                    fontFamily: "poppins,sanserif",
                  }}
                >
                  Remove Photo
                </div>
              </div>
            )}
          </div>

          <input
            type="file"
            id="fileInput"
            style={{ display: "none" }}
            accept="image/*"
            onChange={handleFileChange}
          />
        </div>
        <div>
          {userNameCapitalized && (
            <p
              style={{
                color: "#32406D",
                fontSize: "18px",
                fontWeight: "600",
                marginTop: "-24px",
                overflow: "hidden",
                textAlign: "center",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
                maxWidth: "550px", // adjust width as needed to control truncation length
                cursor: "pointer",
              }}
              title={userNameCapitalized} // this will show the full name on hover as a tooltip
            >
              {userNameCapitalized.slice(0, 15)}{/* show first 15 characters */}
              {userNameCapitalized.length > 15 && "..."}
            </p>
          )}
          <p
            style={{
              color: "#32406D",
              fontSize: "15px",
              fontWeight: "500",
              marginTop: "-9px",
            }}
          >
            {USERTYPE === "recruiter" ? "Recruiter" : "Manager"}
          </p>
          <ul>
            <li onClick={toggleSidebar}>
              <Link
                to="/Dashboard"
                onClick={() => {
                  setToInitial()
                  if (localStorage.getItem("page_no")) {
                    localStorage.removeItem("page_no");
                  }
                }}
                className={
                  pathname.toLowerCase() === "/updatecandidate" || pathname.toLowerCase() === "/editcandidate" || pathname.toLowerCase() === "/dashboard"
                    ? "nav-link active"
                    : "nav-link"
                }
              >
                <span>Dashboard</span>
              </Link>
            </li>
            <li onClick={toggleSidebar}>
              <Link
                to="/PeerReview"
                onClick={() => {
                  setToInitial()
                  if (localStorage.getItem("page_no")) {
                    localStorage.removeItem("page_no");
                  }
                }}
                className={
                  pathname === "/PeerReview"
                    ? "nav-link active"
                    : "nav-link"
                }
              >
                <span>Peer Assigned Profiles</span>
              </Link>
            </li>
            {USERTYPE === "management" ? (
              <li onClick={toggleSidebar}>
                <Link
                  onClick={() => {
                    setToInitial()
                    if (localStorage.getItem("page_no")) {
                      localStorage.removeItem("page_no");
                    }
                  }}
                  to="/JobListing"
                  className={
                    pathname === "/JobListing/AddCandidate" || pathname === "/JobListing" || pathname === "/EditJobPosting" || pathname === "/EditJobStatus"
                      ? "nav-link active"
                      : "nav-link"
                  }
                >
                  <span>Job Listing</span>
                </Link>
              </li>
            ) : null}
            {USERTYPE === "management" ? (
              <li onClick={toggleSidebar}>
                <Link
                  to="/JobAssignments"
                  onClick={() => {
                    setToInitial()
                    if (localStorage.getItem("page_no")) {
                      localStorage.removeItem("page_no");
                    }
                  }}
                  className={
                    pathname === "/JobAssignments"
                      ? "nav-link active"
                      : "nav-link"
                  }
                >
                  <span>Job Assignments</span>
                </Link>
              </li>
            ) : null}
            {USERTYPE === "recruiter" ? (
              <li style={{ position: "relative" }} onClick={toggleSidebar}>
                <Link
                  style={{ paddingRight: "0px" }}
                  onClick={assignRequirementClicked}
                  to="/AssignedRequirements"
                  className={
                    pathname === "/AssignedRequirements/AddCandidate" ||
                      pathname === "/AssignedRequirements"
                      ? "nav-link active"
                      : "nav-link"
                  }
                >
                  <span>Assigned Requirements</span>
                </Link>


                {notificationCount !== 0 && (
                  <div
                    style={{
                      position: "absolute",
                      width: "10px",
                      backgroundColor: "red",
                      top: "1px",
                      color: "white",
                      right: "2px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      padding: "4px 12px 4px 12px",
                      borderRadius: "16px",
                      fontSize: "12px",
                    }}
                  >
                    <div>{notificationCount}</div>
                  </div>
                )}
              </li>
            ) : null}
            <li onClick={toggleSidebar}>
              <Link
                to="/RegisterCandidate"
                onClick={() => {
                  setToInitial()
                  if (localStorage.getItem("page_no")) {
                    localStorage.removeItem("page_no");
                  }
                }}
                className={
                  pathname === "/RegisterCandidate/AddCandidate" ||
                    pathname === "/RegisterCandidate"
                    ? "nav-link active"
                    : "nav-link"
                }
              >
                <span>Add Candidate</span>
              </Link>
            </li>



            < ResumeUpload
              ResumeModal={ResumeModal}

              handleCloseResume={handleCloseResume} />

            {USERTYPE === "management" ? (
              <li onClick={toggleSidebar}>
                <Link
                  onClick={() => {
                    setToInitial()
                    if (localStorage.getItem("page_no")) {
                      localStorage.removeItem("page_no");
                    }
                  }}
                  to="/ProfileTransfer"
                  className={
                    pathname === "/ProfileTransfer"
                      ? "nav-link active"
                      : "nav-link"
                  }
                >
                  <span>Profile Transfer</span>
                </Link>
              </li>
            ) : null}
            <li onClick={toggleSidebar}>
              <Link
                to="/ResumeFetch"
                className={
                  pathname === "/ResumeFetch"
                    ? "nav-link active"
                    : "nav-link"
                }
              >
                <span>Candidate Portal</span>
              </Link>
            </li>
            <li onClick={toggleSidebar}>
              <Link
                to="/NewReport"

                className={
                  pathname === "/NewReport"
                    ? "nav-link active"
                    : "nav-link"
                }
              >
                <span>Analytics</span>
              </Link>
            </li>






            {USERTYPE === "management" ? (
              <li onClick={toggleSidebar}>
                <Link
                  to="/UserAccounts"
                  onClick={() => {
                    setToInitial()
                    if (localStorage.getItem("page_no")) {
                      localStorage.removeItem("page_no");
                    }
                  }}
                  className={
                    pathname === "/AccountDeactivation" ||
                      pathname === "/AccountCreation" ||
                      pathname === "/UserAccounts"
                      ? "nav-link active"
                      : "nav-link"
                  }
                >
                  <span>User Accounts</span>
                </Link>
              </li>
            ) : null}



            <li onClick={toggleSidebar}>
              <Link
                to="/OverView"

                className={
                  pathname === "/OverView"
                    ? "nav-link active"
                    : "nav-link"
                }
              >
                <span>Profile Analysis</span>
              </Link>
            </li>

            <li onClick={toggleSidebar}>
              <Link
                to="/ChangePassword"
                onClick={() => {
                  setToInitial()
                  if (localStorage.getItem("page_no")) {
                    localStorage.removeItem("page_no");
                  }
                }}
                className={
                  pathname === "/ChangePassword"
                    ? "nav-link active"
                    : "nav-link"
                }
              >
                <span>Change Password</span>
              </Link>
            </li>




            <li onClick={handleCalendar} className={`logoutBtn ${showCalendar ? "active" : ""}`}>
              <span id="lgout" className={`logouBtn ${showCalendar ? "actve" : ""}`}>Calendar</span>
            </li>
            < ScheduleMeet
              interviewModal={interviewModal}
              start_autoDate={start_autoDate}
              end_autoDate={end_autoDate}
              startautoTime={startautoTime}
              endautoTime={endautoTime}
              //  resetForm={resetForm}
              setShowCalendar={setShowCalendar}
              InterviewcloseModal={InterviewcloseModal} />

            <li onClick={toggleSidebar}>
              <Link
                to="/RaiseIssue"

                className={
                  pathname === "/RaiseIssue"
                    ? "nav-link active"
                    : "nav-link"
                }
              >
                <span>Help & Support</span>
              </Link>
            </li>

            <li onClick={handleLogout} className={`logoutBtn ${showModal ? "active" : ""}`}>
              <span id="lgout" className={`logouBtn ${showModal ? "actve" : ""}`}>Logout</span>
            </li>

         <li style={{ paddingTop: "50px" }}>
                <ChatBotComponent setTelephonic={setTelephonic} msgs={pathname === "/Dashboard" } chatMsgId={chatMsgId} />
              </li>
         



          </ul>
        </div>

        <Modal
          isOpen={showModal}
          onRequestClose={handleCloseModal}
          contentLabel="Logout Confirmation"
          style={modalStyles}
        >
          <div style={styles.iconContainer}>
            <AlertIcon />
          </div>
          <h2 style={styles.title}>Confirm Logout</h2>
          <p style={styles.subtitle}>Are you sure you want to sign out?</p>
          <div style={styles.buttonContainer}>
            <button onClick={handleCloseModal} style={styles.cancelButton}>
              Cancel
            </button>
            <button onClick={() => {
              handleConfirmLogout(1);
            }} style={styles.logoutButton}>
              Logout
            </button>
          </div>
        </Modal>
        <Modal
          isOpen={isCropping}
          contentLabel="Crop Image"
          className="modal-content"
          overlayClassName="modal-overlay"
          style={{
            overlay: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              zIndex: 9999,
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            },
            content: {
              width: "80%",
              maxWidth: "500px",
              height: "400px",
              background: "white",
              borderRadius: "10px",
              boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.5)",
              padding: "20px",
            },
          }}
        >
          <div style={{ position: "relative", width: "100%", height: "100%" }}>
            <Cropper
              image={imageForCropping}
              crop={crop}
              zoom={zoom}
              aspect={1}
              onCropChange={setCrop}
              onCropComplete={(croppedArea, croppedAreaPixels) =>
                setCroppedAreaPixels(croppedAreaPixels)
              }
              onZoomChange={setZoom}
            />
            <div
              style={{
                position: "absolute",
                bottom: "10px",
                left: "50%",
                transform: "translateX(-50%)",
                zIndex: 1,
              }}
            >
              <Slider
                value={zoom}
                min={1}
                max={3}
                step={0.1}
                aria-labelledby="Zoom"
                onChange={(e, zoom) => setZoom(zoom)}
                style={{ width: "200px" }}
              />
            </div>
            <div style={{ textAlign: "center", marginLeft: "30px" }}>
              <button
                onClick={handleSaveCroppedImage}
                style={{
                  marginRight: "30px",
                  height: "30px",
                  width: "60px",
                  color: "white",
                  backgroundColor: "green",
                  borderRadius: "5px",
                  border: "none",
                }}
              >
                Save
              </button>
              <button
                onClick={() => setIsCropping(false)}
                style={{
                  marginRight: "30px",
                  height: "30px",
                  width: "60px",
                  color: "white",
                  backgroundColor: "red",
                  borderRadius: "5px",
                  border: "none",
                }}
              >
                Cancel
              </button>
            </div>
          </div>
        </Modal>
        <Modal
          isOpen={Telephonic}
          onRequestClose={handleCloseModal}
          contentLabel="Logout Confirmation"
          className="modal-content"
          id="QuestionModal"
          overlayClassName="modal-overlay"
          style={{
            overlay: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              zIndex: 9999,
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            },
            content: {
              width: "57%",
              maxHeight: "98%",

              margin: "auto",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              background: "#f7f7f7",
              borderRadius: "10px",
              boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.5)",
              padding: "20px 40px",
              textAlign: "center",
            },
          }}
        >
          <div className="Modalleft" style={{ display: "flex", justifyContent: "space-between", paddingLeft: "70px" }}>
            <h2 className="Modalheading" style={{ marginBottom: "20px", color: "#32406D" }}>Recruiter-Candidate Interaction Support</h2>
            <MdCancel onClick={handleCloseModal} style={{ cursor: "pointer", color: "#32406d", height: "30px", width: "30px" }} />
          </div>
          <div style={{ marginBottom: "10px", textAlign: "left" }}>
            <label htmlFor="generateQuestions" style={{ fontWeight: "bold", marginRight: "10px", textAlign: "left" }}>
              JD/RESUME:
            </label>
            <select
              style={{ borderRadius: "5px", border: "1px solid #aaa", width: window.innerWidth <= 542 ? '100%' : '50%' }}
              onChange={(e) => {
                setSelectedOptions(e.target.value);
                setFilteredQuestions(null);
                setSelectedDomain(null);
                setJdQuestions([]);

              }}
            >
              <option value="" disabled selected>
                Select an option
              </option>
              <option value="Job_Description">Job Description</option>
              <option value="Resume">Resume</option>
            </select>
          </div>
          {selectedOptions === "Job_Description" && (
            <form className="leftform" onSubmit={handleSubmitJD}>
              {selectedOptions === "Job_Description" && (
                <>
                  {/* Job Description File Upload */}
                  <div style={{ marginBottom: "10px", textAlign: "left" }}>
                    <label htmlFor="jobDescriptionFile" style={{ fontWeight: "bold", marginRight: "10px", textAlign: "left" }}>
                      Job Description
                    </label>
                    <input
                      style={{ padding: "10px", borderRadius: "5px", width: "50%", border: "1px solid #aaa", width: window.innerWidth <= 542 ? '100%' : '400px' }}
                      type="file"
                      name="jobDescriptionFile"
                      id="jobDescriptionFile"
                      accept=".pdf,.doc,.docx"
                      onChange={handleJobdescriptionchange}
                    />
                  </div>

                  {/* Job Description Text Area */}
                  <div style={{ marginBottom: "2px", textAlign: "left" }}>
                    <label htmlFor="jdText" style={{ fontWeight: "bold", marginRight: "10px", textAlign: "left" }}>
                      Job Description Text:
                    </label>
                    <textarea
                      style={{ padding: "10px", borderRadius: "5px", border: "1px solid #aaa", width: "70%", height: "100px", }}
                      id="jdText"
                      rows="6"
                      placeholder="Enter job description here"
                    ></textarea>
                  </div>
                </>
              )}
              {selectedOptions === "Job_Description" && jdQuestions && Object.keys(jdQuestions).length > 0 && (

                <div style={{ padding: "10px", textAlign: "left" }}>
                  <label htmlFor="generateQuestions" style={{ fontWeight: "bold", marginRight: "10px" }}>Types Of Questions:</label>
                  <select
                    value={selectedDomain || ""}
                    onChange={handleDomainChange}
                    style={{ padding: "5px", fontSize: "14px" }}
                  >
                    <option value="">Select a Domain</option>
                    <option value="all">Select All</option>
                    {Object.keys(jdQuestions).map((domain, index) => (
                      <option key={index} value={domain}>
                        {domain}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {selectedDomain && filteredQuestions.length > 0 ? (
                <div style={{ height: "110px", overflowY: "auto", padding: "10px", textAlign: "left" }}>
                  {filteredQuestions.map((group, index) => (
                    <div key={index}>
                      <h4 style={{ fontSize: "16px", marginBottom: "10px", textAlign: "left" }}>
                        {group.domain}:
                      </h4>
                      <ol style={{ paddingLeft: "20px", fontSize: "14px", textAlign: "left" }}>
                        {group.questions.map((question, qIndex) => (
                          <li key={qIndex}>
                            {qIndex + 1}) {question.replace(/[{}"\[\]]/g, '')} {/* Clean question text */}
                          </li>
                        ))}
                      </ol>
                    </div>
                  ))}
                </div>
              ) : selectedDomain ? (
                <p style={{ textAlign: "left", fontSize: "16px", color: "gray" }}>
                  No questions available for the selected domain.
                </p>
              ) : null}
              <div style={{ display: "flex", justifyContent: "space-between", marginTop: "10px" }}>
                <button id="addCandidateSubmit"

                  type="submit"
                  style={{
                    borderRadius: "4px",
                    background: "#32406D",
                    color: "#fff",
                    width: "100px",
                    position: "relative",
                  }}
                >
                  {waitForSubmission ? "" : "Submit"}
                  <ThreeDots
                    wrapperClass="ovalSpinner"
                    wrapperStyle={{
                      position: "absolute",
                      top: "50%",
                      left: "50%",
                      transform: "translate(-50%, -50%)",
                    }}
                    visible={waitForSubmission}
                    height="45"
                    width="45"
                    color="white"
                    ariaLabel="oval-loading"
                  />
                </button>
                <button
                  type="button"
                  onClick={handleDownloadQuestions}
                  style={{
                    borderRadius: '4px',
                    background: '#32406D',
                    color: '#fff',
                    width: '150px',
                    // padding: '10px',
                    border: 'none',
                    cursor: 'pointer',
                    // marginTop: '20px',
                  }}
                >
                  Download Word
                </button>
              </div>
            </form>
          )}
          {selectedOptions === "Resume" && (
            <form className="leftform" onSubmit={handleSubmit}>
              <div style={{ marginBottom: "10px", textAlign: "left" }}>
                <label htmlFor="resume" style={{ fontWeight: "bold", marginRight: "10px" }}>Upload Resume:</label>
                <input
                  style={{ padding: "10px", borderRadius: "5px", border: "1px solid #aaa" }}
                  type="file"
                  name="resume"
                  id="resume"
                  accept=".pdf,.doc,.docx"
                  onChange={handleresumeChange}
                />
              </div>
              <div style={{ marginBottom: "20px", textAlign: "left" }}>
                <label htmlFor="generateQuestions" style={{ fontWeight: "bold", marginRight: "10px" }}>Generate Question:</label>
                <select
                  id="generateQuestions"
                  value={selectedOption}
                  onChange={handleSelectChange}
                  style={{ borderRadius: "5px", border: "1px solid #aaa", width: "100%" }}
                >
                  <option value="" disabled>Select an option</option>
                  <option value="Generate five questions each for location preference, availability to start, salary expectations, and questions for the recruiter.">
                    Generate five questions each for location preference, availability to start, salary expectations, and questions for the recruiter.
                  </option>

                  <option value="Generate five technical questions for each skill in the resume.">
                    Generate five technical questions for each skill in the resume.
                  </option>

                </select>
              </div>
              {interviewData && interviewData.response && interviewData.response[0] && (
                <div >
                  <div style={{ marginBottom: "20px", textAlign: "left" }}>
                    <label htmlFor="generateQuestions" style={{ fontWeight: "bold", marginRight: "10px" }}>Types Of Questions:</label>
                    <select className="selectQ" value={general} onChange={handlegeneralchange}>
                      <option value="">Select a category</option>
                      {
                        Object.keys(interviewData.response[0].questions)?.length > 0 && (<option value="select all">select all</option>)
                      }
                      {Object.keys(interviewData.response[0].questions).map((general, index) => (
                        <option key={index} value={general}>{general}</option>
                      ))}
                    </select>
                  </div>
                  <div style={{ textAlign: "left", color: "green", fontSize: "14px" }}>{interviewData.response[0].heading}</div>
                </div>

              )}
              <div >
                {general && general === "select all" ? (
                  <div style={{ textAlign: "left", overflow: "auto", height: "110px" }}>
                    <ul>
                      {selectAllData?.map((question, index) => (
                        <li key={index}
                          style={{
                            fontSize: indexes && indexes.includes(index) ? "16px" : "14px",
                            fontWeight: indexes && indexes.includes(index) ? "bold" : "normal"
                          }}
                        >{cleanQuestion(question)}
                        </li>
                      ))}
                    </ul>
                  </div>
                ) : (
                  <div style={{ textAlign: "left", overflow: "auto", height: '110px' }}>
                    <ul>
                      {interviewData?.response[0]?.questions[general]?.map((question, index) => (
                        <li key={index}>{cleanQuestion(question)}</li>

                      ))}
                    </ul>
                  </div>
                )}
              </div>
              <div style={{ display: "flex", justifyContent: "space-between", marginTop: "10px" }}>
                <button id="addCandidateSubmit"

                  type="submit"
                  style={{
                    borderRadius: "4px",
                    background: "#32406D",
                    color: "#fff",
                    width: "100px",
                    position: "relative",
                  }}
                >
                  {waitForSubmission ? "" : "Submit"}
                  <ThreeDots
                    wrapperClass="ovalSpinner"
                    wrapperStyle={{
                      position: "absolute",
                      top: "50%",
                      left: "50%",
                      transform: "translate(-50%, -50%)",
                    }}
                    visible={waitForSubmission}
                    height="45"
                    width="45"
                    color="white"
                    ariaLabel="oval-loading"
                  />
                </button>
                <button
                  type="button"
                  onClick={handleDownloadQuestions}
                  style={{
                    borderRadius: '4px',
                    background: '#32406D',
                    color: '#fff',
                    width: '150px',
                    // padding: '10px',
                    border: 'none',
                    cursor: 'pointer',
                    // marginTop: '20px',
                  }}
                >
                  Download Word
                </button>
              </div>


            </form>
          )}


        </Modal>
        <Modal
          isOpen={innerModal}
          onRequestClose={handleCloseInnerModal}
          contentLabel="Logout Confirmation"
          className="modal-content_some"
          overlayClassName="modal-overlay"
          style={{
            overlay: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              zIndex: 9999,
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            },
            content: {
              width: "30%",
              height: "80px",
              margin: "auto",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              background: "#f7f7f7",
              borderRadius: "10px",
              boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.5)",
              padding: "20px 40px",
              textAlign: "center",

            },
          }}
        >
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div style={{ color: "red", marginTop: "10px" }}>
              something went wrong, please try again
            </div>
            <div style={{ textAlign: "right" }}>
              <MdCancel onClick={handleCloseInnerModal} style={{ cursor: "pointer", height: "30px", width: "30px", color: "#32406D", marginTop: "8px" }} />
            </div>
          </div>

        </Modal>
        <Modal
          isOpen={isOpen || showCalendar}
          onRequestClose={() => {
            setIsOpen(false);
            setShowCalendar(false);
          }}
          style={{
            overlay: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              zIndex: 9999,
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            },
            content: {
              width: "60%",
              height: "80%",
              margin: "auto",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              background: "#f7f7f7",
              borderRadius: "10px",
              boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.5)",
              padding: "20px 40px",
              textAlign: "center",

            },
          }}>

          <MdCancel onClick={closeModal} style={{ cursor: "pointer", position: 'absolute', top: '10px', right: '10px', color: "#32406d", height: "30px", width: "30px" }} />

          <div style={{ height: 'calc(100% - 40px)' }}>
            <h2>Calendar</h2>
            <Calendar
              localizer={localizer}
              events={allEvents}
              startAccessor="start"
              endAccessor="end"
              style={{ height: '100%' }}
              views={['month', 'week', 'day']}
              formats={formats}
              onSelectEvent={handleEventClick}
              selectable
              onSelectSlot={handleEmptySlotClick}
            />
          </div>
        </Modal>
        <Modal
          isOpen={showmeet}
          onRequestClose={closemeet}
          contentLabel="Edit Meeting"
          className="modal-content"
          id="QuestionModal"
          overlayClassName="modal-overlay"
          style={{
            overlay: {
              backgroundColor: "transparent",
              zIndex: 9999,
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            },
            content: {
              maxHeight: "98%",
              maxWidth: "28%",
              margin: "auto",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              background: "#f7f7f7",
              borderRadius: "10px",
              boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.5)",
              padding: "29px",
              textAlign: "center",
            },
          }}
        >
          <div className="Modalleft" style={{ display: "flex", justifyContent: "space-between", paddingLeft: "0px" }}>
          </div>
          <div className="Modalleft" style={{ display: "flex", justifyContent: "space-between", paddingLeft: "0px" }}>
            <h2 className="Modalheading" style={{ marginBottom: "5px", fontSize: "25px", color: "rgb(70 68 68 / 95%)" }}>{selectedEvent?.title}</h2>

          </div>
          <form className="leftform"
          >
            {/* {isCallEvent ? ( */}
            {isCallOrInPersonEvent ? (
              <>
                <div style={{ marginBottom: "10px", textAlign: "left" }}>
                  <label htmlFor="resume" style={{ fontWeight: "bold", marginRight: "10px", color: 'rgb(63 63 63)' }}>
                    {selectedEvent ? `${formattedStartDate}` : ""}
                  </label>
                </div>

                {selectedEvent.mobile && (
                  <div style={{ textAlign: "left", color: "#000", marginBottom: "8px" }}>
                    <span style={{ fontWeight: "bold" }}>Mobile :</span> {selectedEvent.mobile}
                  </div>
                )}

                {selectedEvent.candidatereply && (
                  <div style={{ textAlign: "left", color: "#000", marginBottom: "8px" }}>
                    <span style={{ fontWeight: "bold" }}>Candidate Reply :</span>{" "}
                    {selectedEvent.candidatereply.toLowerCase() === "scheduled"
                      ? " Candidate Didn't respond"
                      : selectedEvent.candidatereply}
                  </div>
                )}

                {selectedEvent.candidatereply?.toLowerCase() === "reschedule" &&
                  selectedEvent.reschedule && (
                    <div style={{ textAlign: "left", color: "#000", marginBottom: "8px" }}>
                      <span style={{ fontWeight: "bold" }}>Reschedule Request :</span>{" "}
                      {new Date(selectedEvent.reschedule)
                        .toLocaleDateString("en-GB")
                        .replace(/\//g, "-")}
                    </div>
                  )}

                <div style={{ display: "flex", justifyContent: "Left", marginTop: "10px" }}>

                  <span style={{ borderRadius: "4px", fontSize: "22px", color: "#32406D", position: "relative", display: "flex" }}>
                    {!selectedEvent?.meeting_type ? (

                      <IoLocationOutline />
                    ) : selectedEvent.meeting_type === 'Call' ? (

                      <IoCallSharp />
                    ) : (

                      <IoIosPerson />
                    )}
                  </span>
                  <h4 style={{ marginLeft: '5px', color: '#000' }}>
                    {selectedEvent?.meeting_type === 'Call'
                      ? 'Call Event'
                      : selectedEvent?.meeting_type === 'In-person'
                        ? 'InPerson Meet'
                        : 'ATS Meet Connect'}
                  </h4>
                </div>

                <>
                  <div style={{ display: "flex", justifyContent: "Left", height: "30px", marginTop: "10px" }}>
                    <button
                      type="button"
                      onClick={() => {
                        EditopenModal();
                        setShowCalendar(false);
                        setShowNewEvent(true)
                      }}
                      style={{
                        borderRadius: '4px',
                        marginLeft: '5px',
                        background: 'transparent',
                        border: "1px solid #000",
                        color: '#000',
                        width: '90px',
                        cursor: 'pointer',
                      }}
                    >
                      Edit
                    </button>
                    {!waitForSubmissiondelevent ? (
                      <button
                        type="button"
                        onClick={() => handleDeleteevent(selectedEvent.id)}
                        style={{
                          borderRadius: '4px',
                          marginLeft: '5px',
                          background: 'transparent',
                          border: "1px solid #000",
                          color: 'rgb(238 16 16)',
                          width: '40px',
                          fontSize: "20px",
                          cursor: 'pointer',
                        }}
                      >
                        <MdDelete />
                      </button>
                    ) : (
                      <div style={{ marginLeft: "10px" }}>
                        <TailSpin height="40" width="40" color="#4fa94d" />
                      </div>
                    )}
                  </div>
                </>

                {/* )} */}
              </>
            ) : (
              <>
                <div style={{ marginBottom: "10px", textAlign: "left" }}>
                  <label htmlFor="resume" style={{ fontWeight: "bold", marginRight: "10px", color: 'rgb(63 63 63)' }}>
                    {selectedEvent ? `${formattedStartDate}, ${formattedStartTime} - ${formattedEndTime}` : ""}
                  </label>
                </div>
                <div style={{ display: "flex", justifyContent: "Left", marginTop: "10px" }}>
                  <span style={{ borderRadius: "4px", fontSize: "18px", color: "#32406D", position: "relative" }}>
                    {!selectedEvent?.meeting_type ? (
                      <IoLocationOutline />
                    ) : selectedEvent.meeting_type === 'Teams' ? (

                      <BsMicrosoftTeams />
                    ) : (

                      <SiZoom />
                    )}
                  </span>
                  <h4 style={{ marginLeft: '5px', color: '#000' }}>
                    Meet Connect
                  </h4>
                </div>
                <div style={{ display: "flex", justifyContent: "Left", marginTop: "10px" }}>
                  {selectedEvent && selectedEvent.join_url && (
                    <a
                      href={selectedEvent.join_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{
                        borderRadius: "4px",
                        background: "#32406D",
                        color: "#fff",
                        padding: "10px",
                        width: "100px",
                        position: "relative",
                      }}
                    >
                      Join
                    </a>
                  )}
                  {selectedEvent && localStorage.getItem('email')?.toLowerCase() === selectedEvent.rec_email?.toLowerCase() && (
                    <>
                      <button
                        type="button"
                        onClick={() => {
                          EditopenModal();
                          setShowCalendar(false);
                          setShowNewEvent(false)
                        }}
                        style={{
                          borderRadius: '4px',
                          marginLeft: '5px',
                          background: 'transparent',
                          border: "1px solid #000",
                          color: '#000',
                          width: '90px',
                          cursor: 'pointer',
                        }}
                      >
                        Edit
                      </button>
                      {!waitForSubmissiondel ? (
                        <button
                          type="button"
                          onClick={() => handleDeletemeet(selectedEvent.meeting_id,
                            selectedEvent.meeting_type,         // Assuming 'Zoom' or 'Teams'
                            selectedEvent.event_id)}
                          style={{
                            borderRadius: '4px',
                            marginLeft: '5px',
                            background: 'transparent',
                            border: "1px solid #000",
                            color: 'rgb(238 16 16)',
                            width: '40px',
                            fontSize: "20px",
                            cursor: 'pointer',
                          }}
                        >
                          <MdDelete />
                        </button>
                      ) : (
                        <div style={{ marginLeft: "10px" }}>
                          <TailSpin height="40" width="40" color="#4fa94d" />
                        </div>
                      )}
                    </>
                  )}
                </div>
              </>
            )}
          </form>
        </Modal>
        <Modal
          isOpen={EditModal}
          onRequestClose={EditcloseModal}
          contentLabel="Edit Modal"
          style={{
            overlay: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              zIndex: 9999,
            },
            content: {
              color: "#333",
              top: "50%",
              left: "50%",
              right: "auto",
              bottom: "auto",
              marginRight: "-50%",
              transform: "translate(-50%, -50%)",
              width: showNewEvent
                ? "400px"
                : window.innerWidth <= 542
                  ? "100%"
                  : "550px",
              maxHeight: "630px",
              padding: "0px",
              borderRadius: "12px",
              boxShadow: "0 8px 32px rgba(0, 0, 0, 0.15)",
              position: "relative",
              background: "linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)",
            },
          }}
        >

          {!showNewEvent ? (
            <form onSubmit={handleFormSubmit}>
              <div style={{
                padding: "0px 20px",
                margin: "8px",
                background: "white",
                borderRadius: "12px",
                margin: "8px",
                boxShadow: "0 2px 8px rgba(0,0,0,0.05)",
              }}>
                <div style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  marginBottom: "25px",
                  borderBottom: "2px solid #e9ecef",
                  backgroundColor: "#fff",
                  zIndex: 100,
                  position: "sticky",
                  top: "0px",
                  zIndex: 10,
                  padding: "20px 0px",
                }}>
                  {/* <h2 style={{ color: "#32406D", fontSize: '20px', textAlign: 'center', marginLeft: "-20px" }}>Edit Meeting</h2> */}
                  {/* <button
                      type="button"
                      onClick={handleBackToPlatformSelection}
                      style={{
                        backgroundColor: "transparent",
                        border: "none",
                        color: "black",
                        cursor: "pointer",
                        fontSize: "30px",
                        padding: "1px",
                      }}
                    >
                      ←
                    </button> */}
                  <h2
                    style={{
                      color: "#32406D",
                      fontSize: "20px",
                      textAlign: "center",
                      margin: "0",
                      fontWeight: "600",
                    }}
                  >
                    New {selectedEvent?.meetingType === "teams" ? "Teams" : "Zoom"} Meeting
                  </h2>
                  <div style={{ width: "30px" }}></div>
                </div>
                <div style={{ marginBottom: "20px" }}>
                  <label s style={{
                    display: "block",
                    marginBottom: "8px",
                    fontSize: "16px",
                    textAlign: "left",
                    fontWeight: "500",
                    color: "#495057",
                  }}>Title</label>
                  <input
                    type="text"
                    placeholder="Add title"
                    value={selectedEvent?.title || ''}
                    style={{
                      width: "100%",
                      height: "45px",
                      borderRadius: "8px",
                      border: "2px solid #dee2e6",
                      paddingLeft: "15px",
                      fontSize: "16px",
                      transition: "border-color 0.3s ease",
                      outline: "none",
                    }}
                    onFocus={(e) => (e.target.style.borderColor = "#32406D")}
                    onBlur={(e) => (e.target.style.borderColor = "#dee2e6")}
                    onChange={(e) => setSelectedEvent({ ...selectedEvent, title: e.target.value })}

                  />
                </div>
                <div style={{ marginBottom: "20px" }}>
                  <label style={{
                    display: "block",
                    marginBottom: "8px",
                    fontSize: "16px",
                    textAlign: "left",
                    fontWeight: "500",
                    color: "#495057",
                  }}>
                    Attendees
                  </label>
                  <div ref={inputRef} style={{ position: 'relative' }}>
                    <div style={{
                      display: "flex",
                      alignItems: "center",
                      flexWrap: "nowrap",
                      border: "2px solid #dee2e6",
                      borderRadius: "8px",
                      padding: "8px",
                      overflowX: "auto",
                      overflowY: "hidden",
                      whiteSpace: "nowrap",
                      fontSize: "12px",
                      minHeight: "45px",
                      position: "sticky",
                      right: "0px",
                      background: "white",
                    }}>
                      {selectedEmails1.map((email, idx) => (
                        <span key={idx} style={{
                          display: "inline-flex",
                          alignItems: "center",
                          backgroundColor: "#32406D",
                          color: "white",
                          padding: "6px 12px",
                          borderRadius: "20px",
                          marginRight: "8px",
                          fontSize: "14px",
                          marginBottom: "0px"
                        }}>
                          {email}
                          <button
                            type="button"
                            onClick={() => handleCheckboxChange(email)}
                            style={{
                              background: 'transparent',
                              border: 'none',
                              color: 'white',
                              marginLeft: '8px',
                              cursor: 'pointer',
                              fontSize: '16px'
                            }}
                          >
                            &times;
                          </button>
                        </span>
                      ))}
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          flex: "1",
                          position: "relative",
                        }}
                      >
                        <input
                          type="text"
                          placeholder="Select or search emails"
                          style={{
                            flex: '1',
                            minWidth: '200px',
                            border: 'none',
                            paddingLeft: '10px',
                            fontSize: '16px',
                            outline: 'none',
                            width: 'auto'
                          }}
                          value={searchQuery}
                          // onFocus={() => {
                          //   console.log('Input focused');
                          //   handleFocus();
                          // }}
                          // onBlur={() => {
                          //   console.log('Input blurred');
                          //   handleBlur();
                          // }}
                          onChange={(e) => {
                            console.log('Input changed:', e.target.value);
                            handleSearchChange(e);
                          }}
                        />
                        <button
                          type="button"
                          onClick={() => {
                            console.log('Add button clicked');
                            handleAddNewEmail();
                          }}
                          style={{
                            position: 'sticky',
                            right: '0px',
                            backgroundColor: '#32406D',
                            color: 'white',
                            border: 'none',
                            padding: '5px 10px',
                            borderRadius: '4px',
                            whiteSpace: 'normal',
                            zIndex: '1',
                            display: filteredEmails.length === 0 && searchQuery.trim() ? 'inline-block' : 'none'
                          }}
                        >
                          Add
                        </button>
                      </div>
                    </div>
                    {isDropdownOpen1 && (
                      <div className="dropdown-menu" style={{
                        border: "2px solid #dee2e6",
                        borderRadius: "8px",
                        boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
                        marginTop: "5px",
                        width: "auto",
                        background: "white",
                        maxHeight: "200px",
                        overflowY: "auto",
                      }}>
                        {filteredEmails.length ? (
                          filteredEmails.map((email, idx) => (
                            <label key={idx} className="dropdown-item" style={{
                              display: "flex",
                              padding: "12px",
                              fontWeight: "normal",
                              cursor: "pointer",
                              borderBottom: "1px solid #f8f9fa",
                              transition: "background-color 0.2s ease",
                            }}>
                              <input
                                type="checkbox"
                                value={email}
                                checked={selectedEmails1.includes(email)}
                                onChange={(e) => {
                                  // console.log('Checkbox changed:', e.target.value, e.target.checked);
                                  handleCheckboxChange(email);
                                  setIsDropdownOpen1(false);
                                  setSearchQuery("");
                                }}
                                style={{ marginRight: '5px' }}
                              />
                              {email}
                            </label>
                          ))
                        ) : (
                          <div style={{ padding: '5px' }}>
                            No matching emails found
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
                <div style={{ marginBottom: "20px" }}>
                  <label style={{
                    display: "block",
                    marginBottom: "8px",
                    fontSize: "16px",
                    textAlign: "left",
                    fontWeight: "500",
                    color: "#495057",
                  }}>
                    Attendees (optional)
                  </label>
                  <div ref={dropdownRef} style={{ position: 'relative' }}>
                    <div ref={inputRef} style={{
                      display: "flex",
                      flexWrap: "nowrap",
                      alignItems: "center",
                      border: "2px solid #dee2e6",
                      borderRadius: "8px",
                      padding: "8px",
                      fontSize: "12px",
                      position: "relative",
                      overflowX: "auto",
                      minHeight: "45px",
                      background: "white",
                    }}>
                      {selectedEmails.map((email, idx) => (
                        <span key={idx} style={{
                          display: "inline-flex",
                          alignItems: "center",
                          backgroundColor: "#6c757d",
                          color: "white",
                          padding: "6px 12px",
                          borderRadius: "20px",
                          marginRight: "8px",
                          fontSize: "14px",
                          marginBottom: "0px"
                        }}>
                          {email}
                          <button
                            onClick={() => handleEmailChange1(email)}
                            style={{
                              marginLeft: "8px",
                              cursor: "pointer",
                              fontWeight: "bold",
                              fontSize: "16px",
                            }}
                          >
                            &times;
                          </button>
                        </span>
                      ))}
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          flex: "1",
                        }}
                      >
                        <input
                          type="text"
                          placeholder="Select or search emails"
                          style={{
                            flex: "1",
                            minWidth: "150px",
                            border: "none",
                            paddingLeft: "10px",
                            fontSize: "16px",
                            outline: "none",
                          }}
                          value={searchQuerys}
                          onClick={toggleDropdown}
                          onChange={handleInputChange}
                        />
                        {isDropdownOpen && searchQuerys && filteredManagers.length === 0 && filteredRecruiters.length === 0 && (
                          <button
                            type="button"
                            onClick={handleAddManualEmail}
                            style={{
                              position: "absolute",
                              right: "10px",
                              backgroundColor: "#32406D",
                              color: "white",
                              border: "none",
                              padding: "6px 12px",
                              borderRadius: "6px",
                              fontSize: "14px",
                            }}
                          >
                            Add
                          </button>
                        )}
                      </div>
                    </div>
                    {isDropdownOpen && (filteredManagers.length > 0 || filteredRecruiters.length > 0) && (
                      <div className="dropdown-menu" style={{
                        border: "2px solid #dee2e6",
                        borderRadius: "8px",
                        boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
                        marginTop: "5px",
                        width: "auto",
                        background: "white",
                        maxHeight: "200px",
                        overflowY: "auto",
                      }}>
                        {[...new Set([...filteredManagers, ...filteredRecruiters].map(person => person.email))].map(email => (
                          <label key={email} className="dropdown-item" style={{
                            display: "flex",
                            padding: "12px",
                            fontWeight: "normal",
                            cursor: "pointer",
                            borderBottom: "1px solid #f8f9fa",
                            transition: "background-color 0.2s ease",
                          }}>
                            <input
                              type="checkbox"
                              checked={selectedEmails.includes(email)}
                              onChange={() => handleEmailChange1(email)}
                              style={{ marginRight: '12px' }}
                            />
                            {email}
                          </label>
                        ))}
                        {/* {filteredManagers.length === 0 && filteredRecruiters.length === 0 && (
                        <div style={{ padding: '5px' }}>
                          No matching emails found
                        </div>
                      )} */}
                      </div>
                    )}
                  </div>
                </div>
                <div style={{ marginBottom: "20px" }}>
                  <label style={{
                    display: "block",
                    marginBottom: "8px",
                    fontSize: "16px",
                    textAlign: "left",
                    fontWeight: "500",
                    color: "#495057",
                  }}>Description</label>
                  <textarea

                    rows="4"
                    cols="50"
                    placeholder="Enter meeting description..."
                    style={{
                      width: "100%",
                      border: "2px solid #dee2e6",
                      borderRadius: "8px",
                      paddingLeft: "15px",
                      paddingTop: "12px",
                      fontSize: "16px",
                      resize: "none", // Disable manual resize
                      outline: "none",
                      transition: "border-color 0.3s ease",
                      minHeight: "50px", // Minimum height
                      overflow: "hidden", // Hide scrollbar
                    }}
                    value={selectedEvent?.description}
                    onFocus={(e) => (e.target.style.borderColor = "#32406D")}
                    onBlur={(e) => (e.target.style.borderColor = "#dee2e6")}
                    onInput={(e) => {
                      e.target.style.height = 'auto'; // Reset height to auto
                      e.target.style.height = `${e.target.scrollHeight}px`; // Adjust height based on scrollHeight
                    }}
                    onChange={(e) => setSelectedEvent({ ...selectedEvent, description: e.target.value })}
                  />
                </div>

                <div style={{ marginBottom: "20px" }}>

                  <label style={{
                    display: "block",
                    marginBottom: "8px",
                    fontSize: "16px",
                    textAlign: "left",
                    fontWeight: "500",
                    color: "#495057",
                  }}>
                    Attachment:
                    {(files.length > 0 || existingFiles.length > 0) && (
                      <span style={{
                        fontSize: "14px",
                        color: "#6c757d",
                        fontWeight: "normal",
                      }}>
                        {existingFiles.length + files.length} file(s) selected
                      </span>
                    )}
                  </label>

                  <input
                    type="file"
                    multiple
                    onChange={handleFileChangeevent}
                    accept=".pdf,.doc,.docx"
                    style={{
                      width: "100%",
                      padding: "12px",
                      borderRadius: "8px",
                      border: "2px solid #dee2e6",
                      fontSize: "14px",
                      cursor: "pointer",
                      background: "white",
                    }}
                  />

                  <div style={{
                    marginTop: "10px",
                    padding: "10px",
                    background: "#f8f9fa",
                    borderRadius: "8px",
                    maxHeight: "120px",
                    overflowY: "auto",
                  }}>
                    {Array.isArray(existingFiles) && existingFiles.length > 0 && (
                      <ul style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        padding: "8px",
                        marginBottom: "4px",
                        background: "white",
                        borderRadius: "6px",
                        fontSize: "14px",
                      }}>
                        {existingFiles.map((file, index) => (
                          <li
                            key={index}
                            style={{
                              display: "flex",
                              alignItems: "center",
                              gap: "8px",
                              marginBottom: "4px",
                            }}
                          >
                            <span>{file.filename}</span>
                            <button
                              type="button"
                              onClick={() => handleRemoveExistingFile(index)}
                              style={{
                                background: "#dc3545",
                                border: "none",
                                color: "white",
                                borderRadius: "4px",
                                cursor: "pointer",
                                fontSize: "12px",
                                padding: "4px 8px",
                              }}
                            >
                              Remove
                            </button>
                          </li>
                        ))}
                      </ul>
                    )}
                    {/* New files */}
                    {files.length > 0 && (
                      <ul style={{ padding: 0, listStyle: "none", marginTop: "10px" }}>
                        {files.map((file, index) => (
                          <li key={index} style={{ display: "flex", alignItems: "center", gap: "8px", marginBottom: "4px" }}>
                            <span>{file.name}</span>
                            <button
                              type="button"
                              onClick={() => handleRemoveFile(index)}
                              style={{
                                background: "#dc3545",
                                border: "none",
                                color: "white",
                                borderRadius: "4px",
                                cursor: "pointer",
                                fontSize: "12px",
                                padding: "4px 8px",
                              }}
                            >
                              Remove
                            </button>
                          </li>
                        ))}
                      </ul>
                    )}


                  </div>
                </div>
                <div style={{
                  display: "flex",
                  justifyContent: "space-between",
                  marginBottom: "20px",
                  gap: "15px",
                }}>
                  <div style={{ width: '48%' }}>
                    <label style={{
                      display: "block",
                      marginBottom: "8px",
                      fontSize: "16px",
                      textAlign: "left",
                      fontWeight: "500",
                      color: "#495057",
                    }}>Start Date</label>
                    <input
                      type="date"
                      value={startDateValue}
                      style={{
                        width: "100%",
                        height: "45px",
                        borderRadius: "8px",
                        border: "2px solid #dee2e6",
                        paddingLeft: "15px",
                        fontSize: "16px",
                        outline: "none",
                      }}
                      onChange={handleStartDateChange}
                    />
                  </div>
                  <div style={{ width: '48%' }}>
                    <label style={{
                      display: "block",
                      marginBottom: "8px",
                      fontSize: "16px",
                      textAlign: "left",
                      fontWeight: "500",
                      color: "#495057",
                    }}>End Date</label>
                    <input
                      type="date"
                      value={endDateValue}
                      style={{
                        width: "100%",
                        height: "45px",
                        borderRadius: "8px",
                        border: "2px solid #dee2e6",
                        paddingLeft: "15px",
                        fontSize: "16px",
                        outline: "none",
                      }}
                      onChange={handleEndDateChange}
                    />
                    <span>{error && <p style={{
                      color: 'red', fontSize: "14px",
                      marginTop: "5px",
                    }}>{error}</p>}</span>
                  </div>
                </div>
                <div style={{ marginBottom: '20px' }}>
                  <label style={{
                    display: "block",
                    marginBottom: "8px",
                    fontSize: "16px",
                    textAlign: "left",
                    fontWeight: "500",
                    color: "#495057",
                  }}>Time Zone</label>
                  <select
                    value={selectedEvent?.time_zone || ''}
                    style={{
                      width: "100%",
                      height: "45px",
                      borderRadius: "8px",
                      border: "2px solid #dee2e6",
                      paddingLeft: "15px",
                      fontSize: "16px",
                      outline: "none",
                      background: "white",
                    }}
                    onChange={(e) => setSelectedEvent({ ...selectedEvent, time_zone: e.target.value })}
                  >
                    <option value="">Select a time zone</option>
                    <option value="Asia/Kolkata">Asia/Kolkata</option>
                    <option value="America/New_York">America/New_York</option>
                    <option value="Europe/London">Europe/London</option>
                    <option value="Australia/Sydney">Australia/Sydney</option>
                    <option value="Asia/Tokyo">Asia/Tokyo</option>
                  </select>
                </div>
                <div style={{
                  display: "flex",
                  justifyContent: "space-between",
                  marginBottom: "25px",
                  gap: "15px",
                }}>
                  <div style={{ width: '48%' }}>
                    <label style={{
                      display: "block",
                      marginBottom: "8px",
                      fontSize: "16px",
                      textAlign: "left",
                      fontWeight: "500",
                      color: "#495057",
                    }}>Start Time*</label>
                    <input
                      type="time"
                      list="startTimeOptions"
                      value={selectedEvent.start_time || ''}
                      onChange={(e) => handleStartTimeChange(e)}
                      style={{
                        width: "100%",
                        height: "45px",
                        borderRadius: "8px",
                        border: "2px solid #dee2e6",
                        paddingLeft: "15px",
                        fontSize: "16px",
                        outline: "none",
                      }}
                    />
                    <datalist id="startTimeOptions">
                      {timeOptions.map(option => (
                        <option key={option.value} value={option.value}></option>
                      ))}
                    </datalist>
                  </div>

                  <div style={{ width: '48%' }}>
                    <label style={{
                      display: "block",
                      marginBottom: "8px",
                      fontSize: "16px",
                      textAlign: "left",
                      fontWeight: "500",
                      color: "#495057",
                    }}>End Time*</label>
                    <input
                      type="time"
                      list="endTimeOptions"
                      value={selectedEvent.end_time || ''}
                      onChange={(e) => handleEndTimeChange(e)}
                      style={{
                        width: "100%",
                        height: "45px",
                        borderRadius: "8px",
                        border: "2px solid #dee2e6",
                        paddingLeft: "15px",
                        fontSize: "16px",
                        outline: "none",
                      }}
                    />
                    <datalist id="endTimeOptions">
                      {timeOptions.map(option => (
                        <option key={option.value} value={option.value}></option>
                      ))}
                    </datalist>
                  </div>
                </div>
                <div style={{
                  display: "flex",
                  gap: "15px",
                  justifyContent: "flex-end",
                  paddingTop: "15px",
                  borderTop: "2px solid #e9ecef",
                  position: "sticky",
                  bottom: "8px",
                  background: "#fff"
                }}>
                  <button
                    type="button"
                    style={{
                      backgroundColor: "#dc3545",
                      color: "white",
                      border: "none",
                      borderRadius: "8px",
                      padding: "12px 20px",
                      fontSize: "16px",
                      cursor: "pointer",
                      fontWeight: "500",
                      transition: "all 0.3s ease",
                    }}
                    onClick={EditcloseModal}
                  >
                    Close
                  </button>
                  <button
                    type="submit"
                    style={{
                      backgroundColor: waitForSubmission1
                        ? "#6c757d"
                        : "#32406D",
                      color: "white",
                      border: "none",
                      borderRadius: "8px",
                      padding: "12px 24px",
                      fontSize: "16px",
                      cursor: waitForSubmission1 ? "not-allowed" : "pointer",
                      position: "relative",
                      fontWeight: "500",
                      minWidth: "100px",
                      transition: "all 0.3s ease",
                    }}
                  >
                    {waitForSubmission1 ? "" : "Save"}
                    <ThreeDots
                      wrapperClass="ovalSpinner"
                      wrapperStyle={{
                        position: "absolute",
                        right: "-3px",
                        transform: "translate(-50%, -50%)",
                      }}
                      visible={waitForSubmission1}
                      height="45"
                      width="45"
                      color="white"
                      ariaLabel="oval-loading"
                    />
                  </button>
                </div>
              </div>
            </form>
          ) : (

            <div style={{
              padding: "15px",
              background: "white",
              borderRadius: "12px",
              margin: "5px",
            }}>
              <div className="shedulehed" style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                position: "relative",
                width: "100%",
                marginBottom: "5px",
                paddingBottom: "5px",
                borderBottom: "2px solid #e9ecef",
              }}>
                {/* <button
                type="button"
                onClick={handleBackToPlatformSelection}
                style={{
                  backgroundColor: "transparent",
                  border: "none",
                  color: "black",
                  cursor: "pointer",
                  fontSize: "30px",
                  padding: "1px",
                }}
              >
                ←
              </button> */}
                <div style={{ flex: 1, textAlign: "center" }}>
                  <h3
                    style={{
                      marginBottom: "0",
                      fontSize: "20px",
                      color: "#32406D",
                      fontWeight: "600",
                    }}
                  >
                    {selectedEvent.meeting_type === "call" ? "In Call event" : "In Person event"}  Edit
                  </h3>
                </div>

              </div>
              <div style={{ marginBottom: "10px" }}>
                <label style={{
                  display: "block",
                  marginBottom: "6px",
                  fontSize: "16px",
                  fontWeight: "500",
                  color: "#495057",
                }}> Name*</label>
                <input
                  type="text"
                  value={selectedEvent?.name}
                  // onChange={(e) => setEventName(e.target.value)}
                  onChange={(e) => setSelectedEvent({ ...selectedEvent, name: e.target.value })}
                  placeholder="Enter event name"
                  style={{
                    width: "100%",
                    height: "40px",
                    borderRadius: "8px",
                    border: "2px solid #dee2e6",
                    paddingLeft: "12px",
                    fontSize: "16px",
                    outline: "none",
                  }}
                />
              </div>

              <div style={{ marginBottom: "10px" }}>
                <label style={{
                  display: "block",
                  marginBottom: "6px",
                  fontSize: "16px",
                  fontWeight: "500",
                  color: "#495057",
                }}>Purpose</label>
                <input
                  type="text"
                  value={selectedEvent?.purpose}

                  onChange={(e) => setSelectedEvent({ ...selectedEvent, purpose: e.target.value })}
                  placeholder="Enter purpose"
                  style={{
                    width: "100%",
                    height: "40px",
                    borderRadius: "8px",
                    border: "2px solid #dee2e6",
                    paddingLeft: "12px",
                    fontSize: "16px",
                    outline: "none",
                  }}
                />
              </div>
              <div style={{ marginBottom: "10px" }}>
                <label
                  style={{
                    display: "block",
                    marginBottom: "8px",
                    fontSize: "16px",
                    textAlign: "left",
                    fontWeight: "500",
                    color: "#495057",
                  }}
                >
                  Time *
                </label>
                <input
                  type="time"
                  list="startTimeOptions"
                  value={selectedEvent?.time}
                  // onChange={(e) => setSelectedEvent({ ...selectedEvent, Starttimecallevent: e.target.value })}
                  //  onchange={ handlecalleventStartTimeChange}
                  onChange={(e) => handlecalleventStartTimeChange(e)}
                  style={{
                    width: "100%",
                    height: "45px",
                    borderRadius: "8px",
                    border: "2px solid #dee2e6",
                    paddingLeft: "15px",
                    fontSize: "16px",
                    outline: "none",
                  }}
                />
                <datalist id="startTimeOptions">
                  {timeOptions.map((option) => (
                    <option
                      key={option.value}
                      value={option.value}
                    ></option>
                  ))}
                </datalist>
              </div>
              {selectedEvent.meeting_type === "Call" && (
                <div style={{ marginBottom: "10px" }}>
                  <label style={{
                    display: "block",
                    marginBottom: "6px",
                    fontSize: "16px",
                    fontWeight: "500",
                    color: "#495057",
                  }}>Phone No</label>
                  {/* <input
                  type="number"
                  placeholder="Enter mobile number"
                  value={selectedEvent?.mobile}
                  // onChange={(e) => setMobile(e.target.value)}
                  onChange={(e) => setSelectedEvent({ ...selectedEvent, mobile: e.target.value })}
                  style={{ width: '100%', height: '36px', borderRadius: '4px', border: '1px solid #ccc', paddingLeft: '10px' }}
                /> */}
                  <PhoneInput
                    country={'in'} // No default country
                    value={selectedEvent?.mobile}
                    onChange={(e) => setSelectedEvent({ ...selectedEvent, mobile: e.target.value })}
                    enableSearch={true}
                    inputStyle={{
                      width: '100%',
                      height: '40px',
                      borderRadius: '8px',
                      border: '2px solid #dee2e6',
                      fontSize: '16px',
                      paddingLeft: '12px',
                    }}
                    placeholder="Enter mobile number"
                  />
                </div>
              )}
              {selectedEvent.meeting_type === "In-person" && (
                <div style={{ marginBottom: "15px" }}>
                  <label
                    style={{
                      display: "block",
                      marginBottom: "6px",
                      fontSize: "16px",
                      fontWeight: "500",
                      color: "#495057",
                    }}
                  >
                    Email
                  </label>
                  <input
                    type="email"
                    value={selectedEvent?.email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter email"
                    style={{
                      width: "100%",
                      height: "40px",
                      borderRadius: "8px",
                      border: "2px solid #dee2e6",
                      paddingLeft: "12px",
                      fontSize: "16px",
                      outline: "none",
                    }}
                  />
                </div>
              )}
              {selectedEvent.meeting_type === "In-person" && (
                <div style={{ marginBottom: "20px" }}>
                  <label style={{
                    display: "block",
                    marginBottom: "8px",
                    fontSize: "16px",
                    textAlign: "left",
                    fontWeight: "500",
                    color: "#495057",
                  }}>
                    CC
                  </label>
                  <div ref={dropdownRef} style={{ position: 'relative' }}>
                    <div ref={inputRef} style={{
                      display: "flex",
                      flexWrap: "nowrap",
                      alignItems: "center",
                      border: "2px solid #dee2e6",
                      borderRadius: "8px",
                      padding: "8px",
                      fontSize: "12px",
                      position: "relative",
                      overflowX: "auto",
                      minHeight: "45px",
                      background: "white",
                    }}>
                      {selectedEmails.map((email, idx) => (
                        <span key={idx} style={{
                          display: "inline-flex",
                          alignItems: "center",
                          backgroundColor: "#6c757d",
                          color: "white",
                          padding: "6px 12px",
                          borderRadius: "20px",
                          marginRight: "8px",
                          fontSize: "14px",
                          marginBottom: "0px"
                        }}>
                          {email}
                          <button
                            onClick={() => handleEmailChange1(email)}
                            style={{
                              marginLeft: "8px",
                              cursor: "pointer",
                              fontWeight: "bold",
                              fontSize: "16px",
                            }}
                          >
                            &times;
                          </button>
                        </span>
                      ))}
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          flex: "1",
                        }}
                      >
                        <input
                          type="text"
                          placeholder="Select or search emails"
                          style={{
                            flex: "1",
                            minWidth: "150px",
                            border: "none",
                            paddingLeft: "10px",
                            fontSize: "16px",
                            outline: "none",
                          }}
                          value={searchQuerys}
                          onClick={toggleDropdown}
                          onChange={handleInputChange}
                        />
                        {isDropdownOpen && searchQuerys && filteredManagers.length === 0 && filteredRecruiters.length === 0 && (
                          <button
                            type="button"
                            onClick={handleAddManualEmail}
                            style={{
                              position: "absolute",
                              right: "10px",
                              backgroundColor: "#32406D",
                              color: "white",
                              border: "none",
                              padding: "6px 12px",
                              borderRadius: "6px",
                              fontSize: "14px",
                            }}
                          >
                            Add
                          </button>
                        )}
                      </div>
                    </div>
                    {isDropdownOpen && (filteredManagers.length > 0 || filteredRecruiters.length > 0) && (
                      <div className="dropdown-menu" style={{
                        border: "2px solid #dee2e6",
                        borderRadius: "8px",
                        boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
                        marginTop: "5px",
                        width: "auto",
                        background: "white",
                        maxHeight: "200px",
                        overflowY: "auto",
                      }}>
                        {[...new Set([...filteredManagers, ...filteredRecruiters].map(person => person.email))].map(email => (
                          <label key={email} className="dropdown-item" style={{
                            display: "flex",
                            padding: "12px",
                            fontWeight: "normal",
                            cursor: "pointer",
                            borderBottom: "1px solid #f8f9fa",
                            transition: "background-color 0.2s ease",
                          }}>
                            <input
                              type="checkbox"
                              checked={selectedEmails.includes(email)}
                              onChange={() => handleEmailChange1(email)}
                              style={{ marginRight: '12px' }}
                            />
                            {email}
                          </label>
                        ))}
                        {/* {filteredManagers.length === 0 && filteredRecruiters.length === 0 && (
                        <div style={{ padding: '5px' }}>
                          No matching emails found
                        </div>
                      )} */}
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div style={{ marginBottom: "10px" }}>
                <label style={{
                  display: "block",
                  marginBottom: "8px",
                  fontSize: "16px",
                  textAlign: "left",
                  fontWeight: "500",
                  color: "#495057",
                }}>Date</label>
                <input
                  type="date"
                  value={selectedEvent?.end}
                  // onChange={(e) => setEventDate(e.target.value)}
                  onChange={(e) => setSelectedEvent({ ...selectedEvent, end: e.target.value })}
                  style={{
                    width: "100%",
                    height: "45px",
                    borderRadius: "8px",
                    border: "2px solid #dee2e6",
                    paddingLeft: "15px",
                    fontSize: "16px",
                    outline: "none",
                  }}
                />
              </div>
              {selectedEvent.meeting_type === "In-person" && (
                <div style={{ marginBottom: "15px" }}>
                  <label
                    style={{
                      display: "block",
                      marginBottom: "6px",
                      fontSize: "16px",
                      fontWeight: "500",
                      color: "#495057",
                    }}
                  >
                    Location
                  </label>
                  <input
                    type="text"
                    value={selectedEvent?.location}
                    onChange={(e) => setSelectedEvent({ ...selectedEvent, location: e.target.value })}
                    placeholder="Location"
                    style={{
                      width: "100%",
                      height: "40px",
                      borderRadius: "8px",
                      border: "2px solid #dee2e6",
                      paddingLeft: "12px",
                      fontSize: "16px",
                      outline: "none",
                    }}
                  />
                </div>
              )}

              <div style={{
                display: "flex",
                gap: "15px",
                justifyContent: "flex-end",
                paddingTop: "15px",
                borderTop: "2px solid #e9ecef",
              }} >
                <button
                  type="button"
                  onClick={EditcloseModal}
                  style={{
                    backgroundColor: "#dc3545",
                    color: "white",
                    border: "none",
                    borderRadius: "8px",
                    padding: "12px 20px",
                    fontSize: "16px",
                    cursor: "pointer",
                    fontWeight: "500",
                  }}
                >
                  Close
                </button>
                <button
                  type="submit"
                  onClick={handleEventcallSubmit}
                  style={{
                    backgroundColor: waitForSubmission2 ? "#6c757d" : "#32406D",
                    color: "white",
                    border: "none",
                    borderRadius: "8px",
                    padding: "12px 24px",
                    fontSize: "16px",
                    cursor: waitForSubmission2 ? "not-allowed" : "pointer",
                    position: "relative",
                    fontWeight: "500",
                    minWidth: "100px",
                  }}
                >
                  {waitForSubmission2 ? "" : "Save"}
                  <ThreeDots
                    wrapperClass="ovalSpinner"
                    wrapperStyle={{
                      position: "absolute",
                      right: "-5px",
                      transform: "translate(-50%, -50%)",
                    }}
                    visible={waitForSubmission2}
                    height="45"
                    width="45"
                    color="white"
                    ariaLabel="oval-loading"
                  />
                </button>
              </div>
            </div>
          )}
        </Modal>
        <Modal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          style={{
            overlay: {
              backgroundColor: 'rgba(0, 0, 0, 0.75)',
              zIndex: 9999,
            },
            content: {
              color: 'lightsteelblue',
              top: '50%',
              left: '50%',
              right: 'auto',
              bottom: 'auto',
              marginRight: '-50%',
              transform: 'translate(-50%, -50%)',
              width: "450px",
              height: "150px"
            }
          }}>
          <div style={{ textAlign: 'center', marginTop: "20px" }}>
            <p style={{ color: "#000" }}>{modalMessage}</p>
          </div>
          <div style={{ display: 'flex', justifyContent: 'center', gap: '10px', width: '100%', marginTop: "30px" }}>
            <button
              onClick={() => {
                setIsModalOpen(false);
                if (responseSuccess) {
                  setShowCalendar(true);
                }
              }}
              style={{
                color: "white",
                backgroundColor: "green",
                border: "none",
                width: "50px",
                height: "25px",
                borderRadius: "5px"
              }}
            >
              Ok
            </button>
            <button
              onClick={() => setIsModalOpen(false)}
              style={{
                color: "white",
                backgroundColor: "red",
                border: "none",
                width: "50px",
                height: "25px",
                borderRadius: "5px"
              }}
            >
              Close
            </button>
          </div>
        </Modal>
        <Modal
          isOpen={showImageModal} // Control modal visibility
          onRequestClose={closeModals} // Close modal when clicking outside
          contentLabel="Profile Image"
          style={{
            overlay: {
              backgroundColor: 'rgba(0, 0, 0, 0.7)', // Dark overlay
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center', // Ensures overlay content is centered
              zIndex: 1000, // Make sure modal appears above other content
            },
            content: {
              position: 'relative',
              backgroundColor: '#fff',
              borderRadius: '10px',
              padding: '20px',
              maxWidth: '30vw',
              maxHeight: '70vh',
              margin: 'auto',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              border: 'none',
              outline: 'none', // Removes the default outline
              zIndex: 1001, // Ensures modal content appears above overlay
            },
          }}
        >
          <div style={{ position: 'relative' }}>
            <img
              src={profileImage}
              alt="Profile"
              style={{ maxWidth: '100%', maxHeight: '80vh' }}
            />
            <button
              onClick={closeModals}
              style={{
                position: 'absolute',
                top: '10px',
                right: '10px',
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
                border: 'none',
                borderRadius: '50%',
                padding: '10px',
                cursor: 'pointer',
              }}
            >
              X
            </button>
          </div>
        </Modal>

      </div>
    </div>
  );
})


export default LeftNav;



