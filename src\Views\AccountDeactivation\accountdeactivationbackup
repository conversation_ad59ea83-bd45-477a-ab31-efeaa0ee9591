import React, { useState, useEffect } from "react";
import LeftNav from "../../Components/LeftNav";
import "../../Components/leftnav.css";
import { Link, useNavigate } from "react-router-dom";
import TitleBar from "../../Components/TitleBar";
import "../../Components/titlenav.css";
import { Tooltip as ReactTooltip } from "react-tooltip";
import Modal from "react-modal";
import { toast } from "react-toastify";
import { useRef } from "react";
import {
 
  faTrashAlt,
 
} from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Bounce } from "react-toastify";
// import "../UserAccounts/UserAccount.css";
import { Hourglass } from "react-loader-spinner";
import { Radio, ThreeDots } from "react-loader-spinner";
import { useSelector } from "react-redux";
import { MdOutlineYoutubeSearchedFor } from "react-icons/md";
import { IoMdSearch } from "react-icons/io";
import { useDispatch } from "react-redux";
import { MdDelete } from "react-icons/md";
import { MdFilterAlt } from "react-icons/md";
import { FaAngleLeft } from "react-icons/fa6";
import filter_icon from '../../assets/filter_icon.svg'
import clear_search from '../../assets/clear_search.svg'
import { FaAngleRight } from "react-icons/fa6";
// import { getAllRecruitersManagers } from "../utilities.js";
import { } from // setActiveUsers,

  "../../store/slices/userSlice";
import "../AccountDeactivation/AccountDeactivation.css";

import {
  getDashboardData,
  getAllJobs,
  getAllRecruitersManagers,
} from "../utilities.js";

function AccountDeactivation() {
  const uniRef = useRef(null);
  const dispatch = useDispatch();
  const { recruiters, managers } = useSelector(
    (state) => state.userSliceReducer,
  );
   const navigate = useNavigate();

  useEffect(() => {
    if (recruiters.length === 0 || managers.length === 0) {
      setLoading(true);
    } else {
      console.log("fetchUsers useff");
      fetchUsers();
      setLoading(false);
    }
  }, [recruiters, managers]);

  const [belowCount, setBelowCount] = useState(0);
  const [id, setId] = useState(1);
  const [countItems, setCountItems] = useState(0);
  const [filteredId, setFilteredId] = useState([]);
  const [searchValue, setSearchValue] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [users, setUsers] = useState([]);
  const [deactivateusers, setdeactivateUsers] = useState([]);
  const [toggleDisabled, setToggleDisabled] = useState(false);

  //filter
  const [selectAll, setSelectAll] = useState(false);
  const [uniqueDataname, setUniqueDataname] = useState([]);
  const [nameSelected, setnameSelected] = useState([]);

  const [selectAllUsername, setSelectAllUsername] = useState(false);
  const [uniqueDataUsernames, setUniqueDataUsernames] = useState([]);
  const [usernameSelected, setUsernameSelected] = useState([]);

  const [selectAlldeactivename, setSelectAllDeactivename] = useState(false);
  const [uniqueDatadeactivename, setUniqueDatadeactivename] = useState([]);
  const [deactivenameSelected, setdeactivenameSelected] = useState([]);

  const [selectAllEmail, setSelectAllEmail] = useState(false);
  const [uniqueDataEmails, setUniqueDataEmails] = useState([]);
  const [emailSelected, setEmailSelected] = useState([]);

  const [selectAllUserType, setSelectAllUserType] = useState(false);
  const [uniqueDataUserTypes, setUniqueDataUserTypes] = useState([]);
  const [userTypeSelected, setUserTypeSelected] = useState([]);

  useEffect(() => {
    const closeFilterPop = (e) => {
      const allRefIds = [
        "username_ref",
        "name_ref",
        "deactivename_ref",
        "email_ref",
        "usertype_ref",

        "user_label_nameRef",
        "deactivename_label_Ref",
        "usertype_label_ref",
        "name_label_ref",
        "email_label_ref",


      ];
      let bool = false;
      for (const ref of allRefIds) {
        if (document.getElementById(ref)?.contains(e.target)) {
          bool = true;
          return;
        }
      }
      if (uniRef.current?.contains(e.target) || bool) {
        console.log("yes");
      } else {
        console.log("no");
        setshowSearchUseraccount((prev) => ({
          ...Object.fromEntries(Object.keys(prev).map((key) => [key, false])),
        }));
      }
    };
    document.addEventListener("click", closeFilterPop);
    return () => {
      document.removeEventListener("click", closeFilterPop);
    };
  }, []);

  const [showSearchUseraccount, setshowSearchUseraccount] = useState({
    showSearchname: false,
    showSearchUsername: false,
    showSearchEmail: false,
    showSearchUsertype: false,
    showSearchdeactivatename:false,
  });

  const [isnameFiltered, setIsNameFiltered] = useState(false);
  const [isdeactivenameFiltered, setIsDeactiveNameFiltered] = useState(false);
  const [isemailFiltered, setIsEmailFiltered] = useState(false);
  const [isusertypeFiltered, setIsUsertypeFiltered] = useState(false);
  const [isusernameFiltered, setIsUsernameFiltered] = useState(false);

  const handleOkClick = () => {
    setId(1)
    updateFilteredRows({
      usernameSelected,
      nameSelected,
      emailSelected,
      deactivenameSelected,
      userTypeSelected,

      setUsernameSelected,
      setdeactivenameSelected,
      setUniqueDataEmails,
      setUserTypeSelected,

      setSelectAll,
      setSelectAllUsername,
      setSelectAllDeactivename,
      setSelectAllEmail,
      setSelectAllUserType,

      setUniqueDataname,
      setUniqueDatadeactivename,
      setUniqueDataUsernames,
      setUniqueDataEmails,
      setUniqueDataUserTypes,
    });
    // Set the filtered rows
    setIsNameFiltered(nameSelected.length > 0);
    setIsDeactiveNameFiltered(nameSelected.length > 0);
    setIsEmailFiltered(emailSelected.length > 0);
    setIsUsernameFiltered(usernameSelected.length > 0);
    setIsDeactiveNameFiltered(deactivenameSelected.length > 0);
    setIsUsertypeFiltered(userTypeSelected.length > 0);
    // Hide the filter popups
    // setshowSearchUseraccount((prev) =>
    //   Object.fromEntries(
    //     Object.entries(prev).map(([key, value]) => [key, false]),
    //   ),
    // );
  };

  useEffect(() => {
    // console.log("handleOkClick Called");
    handleOkClick();
  }, [nameSelected, userTypeSelected, usernameSelected, emailSelected,deactivenameSelected]);

  const [filteredRows, setFilteredRows] = useState([]);
  const [nameOfNonEmptyArray, setnameOfNonEmptyArray] = useState(null);
  const updateFilteredRows = ({
    usernameSelected,
    nameSelected,
    emailSelected,
    deactivenameSelected,
    userTypeSelected,

    setUniqueDataname,
    setUniqueDataUsernames,
    setUniqueDatadeactivename,
    setUniqueDataEmails,
    setUniqueDataUserTypes,
  }) => {
    let prevfilteredRows = [...recruiters, ...managers];
    console.log("prevfilteredRows 1", prevfilteredRows);
    if (usernameSelected.length > 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        usernameSelected.includes(row.username.toLowerCase()),
      );
    }
    if (nameSelected.length > 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        nameSelected.includes(row.name.toLowerCase()),
      );
    }
    if (deactivenameSelected.length > 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        deactivenameSelected.includes(row.deactivated_by?.toLowerCase()),
      );
    }
    if (emailSelected.length > 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        emailSelected.includes(row.email.toLowerCase()),
      );
    }

    if (userTypeSelected.length > 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        userTypeSelected.includes(row.user_type.toLowerCase()),
      );
    }

    const arrayNames = [
      "usernameSelected",
      "nameSelected",
      "deactivenameSelected",
      "emailSelected",
      "userTypeSelected",
    ];
    const arrays = [
      usernameSelected,
      nameSelected,
      deactivenameSelected,
      emailSelected,
      userTypeSelected,
    ];
    let NamesOfNonEmptyArray = [];

    arrays.forEach((arr, index) => {
      if (arr.length > 0) {
        // NameOfNonEmptyArray = arrayNames[index];
        NamesOfNonEmptyArray.push(arrayNames[index]);
        // setNonEmptyArray(prev => ([
        //   ...prev,
        //   arrayNames[index]
        // ]))
      }
    });


    if (!NamesOfNonEmptyArray.includes("nameSelected")) {
      setUniqueDataname(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.name;
            }),
          ),
        );
      });
    }
    if (!NamesOfNonEmptyArray.includes("deactivenameSelected")) {
      setUniqueDatadeactivename(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.deactivated_by;
            }),
          ),
        );
      });
    }
    if (!NamesOfNonEmptyArray.includes("usernameSelected")) {
      setUniqueDataUsernames(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.username;
            }),
          ),
        );
      });
    }
    if (!NamesOfNonEmptyArray.includes("emailSelected")) {
      setUniqueDataEmails(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.email;
            }),
          ),
        );
      });
    }
    if (!NamesOfNonEmptyArray.includes("userTypeSelected")) {
      setUniqueDataUserTypes(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.user_type;
            }),
          ),
        );
      });
    }
    console.log("prevfilteredRows 2", prevfilteredRows);
    setFilteredRows(prevfilteredRows);
    // console.log(prevfilteredRows.length)
    setBelowCount(prevfilteredRows?.length);
  };
  useEffect(() => {
    // console.log(belowCount)
  }, [belowCount]);
  const handleSelectAll = () => {
    const allChecked = !selectAll;
    setSelectAll(allChecked);

    if (allChecked) {
      setnameSelected(uniqueDataname.map((d) => d.toLowerCase()));
    } else {
      setnameSelected([]);
    }
  };

  const handleCheckboxChangename = (name) => {
    const isSelected = nameSelected.includes(name);
    if (isSelected) {
      setnameSelected((prevSelected) =>
        prevSelected.filter((item) => item !== name),
      );
      setSelectAll(false);
    } else {
      setnameSelected((prevSelected) => [...prevSelected, name]);
      setSelectAll(nameSelected.length === uniqueDataname.length - 1);
    }
  };
  const handleSelectdeactivenameAll = () => {
    const allChecked = !selectAlldeactivename;
    setSelectAllDeactivename(allChecked);

    if (allChecked) {
      setdeactivenameSelected(uniqueDatadeactivename.map((d) => d.toLowerCase()));
    } else {
      setdeactivenameSelected([]);
    }
  };

  const handleCheckboxChangedeactivename = (deactivated_by) => {
    const isSelected = deactivenameSelected.includes(deactivated_by);
    if (isSelected) {
      setdeactivenameSelected((prevSelected) =>
        prevSelected.filter((item) => item !== deactivated_by),
      );
      setSelectAll(false);
    } else {
      setdeactivenameSelected((prevSelected) => [...prevSelected, deactivated_by]);
      setSelectAllDeactivename(deactivenameSelected.length === uniqueDatadeactivename.length - 1);
    }
  };
  const handleSelectAllForUsername = () => {
    const allChecked = !selectAllUsername;
    setSelectAllUsername(allChecked);

    if (allChecked) {
      setUsernameSelected(uniqueDataUsernames.map((d) => d.toLowerCase()));
    } else {
      setUsernameSelected([]);
    }
  };

  const handleCheckboxChangeUsername = (username) => {
    const isSelected = usernameSelected.includes(username);
    if (isSelected) {
      setUsernameSelected((prevSelected) =>
        prevSelected.filter((item) => item !== username),
      );
      setSelectAllUsername(false);
    } else {
      setUsernameSelected((prevSelected) => [...prevSelected, username]);
      setSelectAllUsername(
        usernameSelected.length === uniqueDataUsernames.length - 1,
      );
    }
  };

  const handleSelectAllForEmail = () => {
    const allChecked = !selectAllEmail;
    setSelectAllEmail(allChecked);

    if (allChecked) {
      setEmailSelected(uniqueDataEmails.map((d) => d.toLowerCase()));
    } else {
      setEmailSelected([]);
    }
  };

  const handleCheckboxChangeEmail = (email) => {
    const isSelected = emailSelected.includes(email);
    if (isSelected) {
      setEmailSelected((prevSelected) =>
        prevSelected.filter((item) => item !== email),
      );
      setSelectAllEmail(false);
    } else {
      setEmailSelected((prevSelected) => [...prevSelected, email]);
      setSelectAllEmail(emailSelected.length === uniqueDataEmails.length - 1);
    }
  };

  const handleSelectAllForUserType = () => {
    const allChecked = !selectAllUserType;
    setSelectAllUserType(allChecked);

    if (allChecked) {
      setUserTypeSelected(uniqueDataUserTypes.map((d) => d.toLowerCase()));
    } else {
      setUserTypeSelected([]);
    }
  };

  const handleCheckboxChangeUserType = (userType) => {
    const isSelected = userTypeSelected.includes(userType);
    if (isSelected) {
      setUserTypeSelected((prevSelected) =>
        prevSelected.filter((item) => item !== userType),
      );
      setSelectAllUserType(false);
    } else {
      setUserTypeSelected((prevSelected) => [...prevSelected, userType]);
      setSelectAllUserType(
        userTypeSelected.length === uniqueDataUserTypes.length - 1,
      );
    }
  };

  useEffect(() => {
    // Save users to local storage whenever users change
    localStorage.setItem("users", JSON.stringify(users));
  }, [users]);

  // useEffect(() => {
  //   // Retrieve users from local storage on component mount
  //   const storedUsers = localStorage.getItem("users");
  //   if (storedUsers) {
  //     setUsers(JSON.parse(storedUsers));
  //   }
  // }, []);

  const fetchUsers = async () => {
    console.log("fetchUsers");
    try {
      // const data =
      // console.log('active_users',data)
      const loggedInUserName = localStorage.getItem("user_name");
      const combinedUsers = [...managers, ...recruiters].filter(
        (user) => user.username !== loggedInUserName,
      );
      // console.log("combinedUsers", combinedUsers);
      setUsers(combinedUsers);
      // dispatch(setActiveManagers({users:data.active_users_manager}))
      // dispatch(setActiveRecruiters({users:data.active_users_recruiter}))
      setBelowCount(combinedUsers.length);
      if (combinedUsers.length > 0) {
        console.log("combinedUsers.lengt", combinedUsers.length);
        // setUsers(combinedUsers); // Assign fetched data to state
        // console.log(combinedUsers.length);
        const temp = combinedUsers.length;
        if (temp % 30 === 0) {
          setCountItems(temp / 30);
        } else {
          setCountItems(temp / 30 + 1);
        }

        // Set unique data for filters
        setUniqueDataname([...new Set(combinedUsers.map((d) => d.name))]);
        setUniqueDataUsernames([
          ...new Set(combinedUsers.map((d) => d.username)),
        ]);
        setUniqueDatadeactivename([
          ...new Set(combinedUsers.map((d) => d.deactivated_by)),
        ]);
        setUniqueDataEmails([...new Set(combinedUsers.map((d) => d.email))]);
        setUniqueDataUserTypes([
          ...new Set(combinedUsers.map((d) => d.user_type)),
        ]);

        setFilteredRows(
          combinedUsers?.filter(
            (item) => item.username !== localStorage.getItem("user_name"),
          ),
        );
      }
      // dispatch(setActiveUsers({ users: combinedUsers }));
      // console.log("active users",activeUsers)
    } catch (err) {
      setError(err.message);
    }
  };

  // useEffect(() => {
  //   // Indicate loading has completed

  //   const fun1 = async () => {
  //     setBelowCount(activeUsers.length);
  //     // console.log(activeUsers)
  //     if (activeUsers.length > 0) {
  //       setUsers(activeUsers); // Assign fetched data to state
  //       // console.log(combinedUsers.length);
  //       const temp = activeUsers.length;
  //       if (temp % 30 === 0) {
  //         setCountItems(temp / 30);
  //       } else {
  //         setCountItems(temp / 30 + 1);
  //       }
  //       setLoading(false);

  //       // Set unique data for filters
  //       setUniqueDataname([...new Set(activeUsers.map((d) => d.name))]);
  //       setUniqueDataUsernames([
  //         ...new Set(activeUsers.map((d) => d.username)),
  //       ]);
  //       setUniqueDataEmails([...new Set(activeUsers.map((d) => d.email))]);
  //       setUniqueDataUserTypes([
  //         ...new Set(activeUsers.map((d) => d.user_type)),
  //       ]);

  //       setFilteredRows(
  //         activeUsers?.filter(
  //           (item) => item.username !== localStorage.getItem("user_name"),
  //         ),
  //       );
  //     }
  //   };
  //   fun1();
  //   // Set unique data for filters
  //   // Set filtered rows initially to all users
  //   // console.log('filteredRows:',active_users_recruiter)
  // }, [activeUsers]);

  const toggleUsers = async (username, userStatus) => {
    try {
      setToggleDisabled(true); // Disable toggle during API call

      const response = await fetch(
        " https://starfish-app-txle5.ondigitalocean.app/deactivate_user",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            user_name: username,
            user_status: userStatus ? false : true,
            user_id: localStorage.getItem("user_id"),
            deactivated_name: localStorage.getItem("name")
          }),
        },
      );

      if (response.ok) {
        await getAllRecruitersManagers().then(() => {
          const updatedUsers = users.map((user) =>
            user.username === username
              ? { ...user, is_active: !userStatus }
              : user,
          );
          // console.log("Updated Users:", updatedUsers); // Debugging log
          setUsers(updatedUsers);
          const action = userStatus ? "deactivated" : "activated";
          const message = `Account ${username} is ${action}`;
          if (userStatus) {
            toast.success(message, { className: "toast-error" }); // Red color for deactivated
          } else {
            toast.success(message, { className: "toast-success" }); // Green color for activated
          }
        });
        // console.log("API Response:", data);
      } else {
        console.log("API Error:", response.statusText);
        notify();
      }
    } catch (err) {
      console.error("API Request Error:", err);
    } finally {
      setToggleDisabled(false); // Enable toggle after API call completes (success or failure)
    }
  };

  const notify = () => toast.error("Select other than the current account");
  const goToPage = (pageNumber) => {
    if (pageNumber >= 1 && pageNumber <= countItems) {
      setId(pageNumber);
    }
  };

  // Calculate the range of pages to display in pagination
  const getPageRange = () => {
    const pageRange = [];
    const maxPagesToShow = 5; // Adjust this value to show more or fewer page numbers

    // Determine the start and end page numbers to display
    let startPage = Math.max(1, id - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(countItems, startPage + maxPagesToShow - 1);

    // Adjust startPage and endPage if near the beginning or end
    if (endPage - startPage < maxPagesToShow - 1) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    // Include ellipsis if necessary
    if (startPage > 1) {
      pageRange.push(1);
      if (startPage > 2) {
        pageRange.push("...");
      }
    }

    // Add page numbers to the range
    for (let i = startPage; i <= endPage; i++) {
      pageRange.push(i);
    }

    // Include ellipsis if necessary
    if (endPage < countItems) {
      if (endPage < countItems - 1) {
        pageRange.push("...");
      }
      pageRange.push(countItems);
    }

    return pageRange;
  };
  function extractKeyValuePairs(object, keysToExtract) {
    return keysToExtract.reduce((acc, key) => {
      if (key in object) {
        acc[key] = object[key];
      }
      return acc;
    }, {});
  }
  const fun = (update) => {
    const list = update.filter((it) => {
      return filteredRows.some((item) => item.id === it.id);
    });
    // console.log(list);
    setBelowCount(list?.length);
    setId(1);
    // setUniqueDataname([...new Set(list.map((d) => d.name))]);
    // setUniqueDataUsernames([...new Set(list.map((d) => d.username))]);
    // setUniqueDataEmails([...new Set(list.map((d) => d.email))]);
    // setUniqueDataUserTypes([...new Set(list.map((d) => d.user_type))]);
  };
  useEffect(() => {
    // console.log(searchValue)
    if (users?.length > 0) {
      const update = users?.filter((item) => {
        const extractedObj = extractKeyValuePairs(item, [
          "id",
          "username",
          "name",
          "email",
          "user_type",
          "deactivated_by"
        ]);
        // console.log(extractedObj);
        for (const key in extractedObj) {
          if (key === "id") {
            continue;
          }
          // console.log('key',key)
          let val = extractedObj[key];
          // console.log(val)
          if (val !== null && val !== undefined) {
            if (typeof val !== "string") {
              val = val.toString();
            }
            if (val.toLowerCase().includes(searchValue.toLowerCase())) {
              // console.log('yes working good')
              return true;
            }
          } else {
            console.log("Value is null or undefined for key:", key);
          }
        }
        console.log("No match found for searchValue:", searchValue);
        return false;
      });

      // console.log(update)
      fun(update);
      // setBelowCount(update.length)
      let extract = [];
      for (const item of update) {
        extract.push(item.id);
      }
      setFilteredId(extract);
      // console.log(extract)
    }
  }, [searchValue]);
  // if (loading) {
  //   return <div>Loading...</div>;
  // }
  useEffect(() => {
    if (belowCount % 30 != 0) setCountItems(parseInt(belowCount / 30) + 1);
    else setCountItems(parseInt(belowCount / 30));
  }, [belowCount]);

  const filteredData = (data) => {
    if (data?.length > 3) {
      const data1 = data.filter(
        (_, idx) => idx + 1 <= id * 30 && idx + 1 > (id - 1) * 30,
      );
      return data1;
    } else {
      return data;
    }
  };

  const displayItems = () => {
    // console.log('filteredRows:',filteredRows)
    const data = filteredRows?.filter((item) => {
      if (filteredId.length > 0) {
        for (const it of filteredId) {
          if (it === item.id) {
            return true;
          }
        }
        // Return false only if none of the elements in filteredId match item.id
        return false;
      } else {
        if (searchValue === "") return true;
        else return false;
      }
    });

    const data1 = filteredData(data);
    return data1;
  };

  if (error) {
    return <div>Error: {error}</div>;
  }


  const removeAllFilter = () => {
    setnameSelected([])
    setUsernameSelected([])
    setEmailSelected([])
    setdeactivenameSelected([])
    // setdeactivateuserSelected([])
    setUserTypeSelected([])
  }
  const [waitForSubmission, setwaitForSubmission] = useState(false);
     const [showModaldel, setShowModaldel] = useState(false);
       const [delId, setDelId] = useState(null);
  const deleteFunction = (id) => {
    // localStorage.setItem('deleteRow',true)
    setDelId(id);
    setShowModaldel(true);
  };

  const handleCloseModaldel = () => {
    setShowModaldel(false);
  };

  const handleDeleteCandidate = async () => {
     if (!waitForSubmission) {
       setwaitForSubmission(true);
       // console.log("delete");
       try {
         const response = await fetch(
           // `api//delete_candidate/${delId}`,{
           " https://starfish-app-txle5.ondigitalocean.app/delete_user",
           {
             method: "POST",
             headers: {
               "Content-Type": "application/json",
             },
             body: JSON.stringify({
              user_id:delId,
              //  user_id: localStorage.getItem("user_id"),
             }),
           },
         );
      
         if (response.ok) {

          const data = await response.json();
          //  const range = getPageRange();
          //  if (id === range.length && filteredRows.length % 60 === 1) {
          //    setId((id) => id - 1);
          //    // console.log(id - 1);
          //    setBelowCount(filteredRows.length - 1);
          //  }
           // console.log("ok");
 
          //  deleteRow = true;
           getAllRecruitersManagers().then(() => {
             setShowModaldel(false);
             toast.success(data.message);
             setwaitForSubmission(false);
           });
           // to get new daata after deletion
         } else {
           setwaitForSubmission(false);
           toast.error(data.message);
           // console.log("delete not happened");
           // console.log(response.statusText);
         }
       } catch (err) {
         setwaitForSubmission(false);
         console.log(err);
       }
     }
   };



  const [showModal, setShowModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState({ username: "", isActive: false });
  const [modalMessage, setModalMessage] = useState("");
  const handleOpenModal = (username, isActive) => {
    const action = isActive ? "Deactivate" : "Activate";
    setModalMessage(`Are you sure you want to ${action} ${username}?`);
    setSelectedUser({ username, isActive });
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);

  };

  return (
    <div className="wrapper">
      <LeftNav />
      <div className="section">
        <TitleBar />
        {loading ? (
          <div className="loader-container">
            <Hourglass
              height="60"
              width="340"
              ariaLabel="hourglass-loading"
              wrapperStyle={{}}
              wrapperClass=""
              colors={["#306cce", "#72a1ed"]}
            />
          </div>
        ) : (
          <>
            <div
            className="mobiledash useraccco"
            style={{margin :"35px 0px 7px"}} >
             <div className="left-section">
                  <button className="useracccot" onClick= {()=>navigate('/AccountCreation')}> Account Creation</button>
                  <button className="useracccot" onClick= {()=>navigate('/UserAccounts')}> Return Accounts</button>
               </div>
               <div className="AUheading center-section">
            <h5 style={{ paddingTop: "0px", 
              // margin: "-35px 0 10px" 
              }} className="users">
              Account Deactivation
            </h5>
            </div>
              <div style={{ display: 'flex', alignItems: 'center',zIndex:'2' }} >
                <IoMdSearch style={{ display: 'flex', alignItems: 'center', height: "22px", width: "22px", marginRight: "-25px", marginTop: "5px" }} className="right-section" />
                <input
                className="UAsearch"
                  placeholder="Search"
                  style={{
                    marginTop: "4px",
                    paddingLeft: "26px",
                    height: "30px",
                    width: "250px",
                    backgroundColor: "rgba(255, 255, 255, 0.80)",
                    border: "none",
                    borderRadius: "5px",
                    padding: "0 25px"
                  }}
                  value={searchValue}
                  onChange={(e) => {
                    // console.log(e.target.value);
                    setSearchValue(e.target.value);
                    // date_created    job_id    name    email   mobile   client    profile    skills    recruiter    status
                  }}
                />
                {/* <button style={{marginLeft:'20px',backgroundColor: "#32406d",color:'white',border:'none',padding:'4px',borderRadius:'5px'}}
                  onClick={removeAllFilter}
>clear all filters</button> */}
                {/* <img style={{marginLeft:'20px',height:'24px'}} src={clear_search} alt="svg_img" /> */}
                <div className="remove_filter_icons" onClick={() => {
                  setSearchValue('');
                }} style={{ display: 'flex', marginLeft: '10px', padding: '3px', justifyContent: 'center', alignItems: 'center', borderRadius: "5px", marginTop: "4px" }}>
                  {/* <img style={{ cursor: 'pointer', height: '24px' }} src={clear_search} alt="svg_img"
                        data-tooltip-id={"remove_search"}
                        data-tooltip-content="Clear search"
                    /> */}
                  <MdOutlineYoutubeSearchedFor style={{ cursor: 'pointer', height: '24px', width: "24px", color: "#32406d" }} data-tooltip-id={"remove_search"}
                    data-tooltip-content="Clear search" />
                  <ReactTooltip
                    style={{ zIndex: 999, padding: "2px", backgroundColor: "#32406d" }}
                    place="top-start"
                    id="remove_search"
                  />
                </div>
                <div className="remove_filter_icons" onClick={removeAllFilter} style={{ display: 'flex', marginLeft: '10px', padding: '3px', justifyContent: 'center', alignItems: 'center', borderRadius: "5px", marginTop: "4px" }}>
                  <img style={{ cursor: 'pointer', height: '24px' }} src={filter_icon} alt="svg_img"
                    data-tooltip-id={"remove_filter"}
                    data-tooltip-content="Clear all filters"
                  />
                  <ReactTooltip
                    style={{ zIndex: 999, padding: "4px", backgroundColor: "#32406d" }}
                    place="top-start"
                    id="remove_filter"
                  />
                </div>
              </div>
            </div>
            
            <div
              className="container"
              style={{ marginTop: "0px" }}
            >
              <div
                className="table-container"
                style={{ overflowY: "auto", overflowX: "auto", marginTop: "3px" }}
              >
                <table
                  style={{
                    width: "100%",
                    overflow: "auto",
                    tableLayout: "fixed",
                    margin: "0"
                  }}
                  className="table userac"
                  id="myTable"
                >
                  <thead>
                    <tr>
                      <th style={{ width: "150px", color: showSearchUseraccount.showSearchUsername ? "orange" : "white" }}>
                        <span
                          style={{ cursor: "pointer" }}
                          id={"user_label_nameRef"}
                          onClick={() => {
                            setshowSearchUseraccount((prev) => ({
                              ...Object.fromEntries(
                                Object.keys(prev).map((key) => [
                                  key,
                                  key === "showSearchUsername"
                                    ? !prev.showSearchUsername
                                    : false,
                                ]),
                              ),
                            }));
                          }}
                        >Username</span>
                        <MdFilterAlt
                          style={{
                            color: isusernameFiltered ? "orange" : "white",
                          }}
                          id={"username_ref"}
                          className="arrow"
                          onClick={() => {
                            setshowSearchUseraccount((prev) => ({
                              ...Object.fromEntries(
                                Object.keys(prev).map((key) => [
                                  key,
                                  key === "showSearchUsername"
                                    ? !prev.showSearchUsername
                                    : false,
                                ]),
                              ),
                            }));
                          }}
                        />
                        {showSearchUseraccount.showSearchUsername && (
                          <div ref={uniRef} className="Filter-popup">
                            <form
                              id="filter-form"
                              className="Filter-inputs-container"
                            >
                              <ul>
                                <li>
                                  <input
                                    type="checkbox"
                                    style={{
                                      width: "12px",
                                      marginRight: "5px",
                                    }}
                                    checked={selectAllUsername}
                                    onChange={handleSelectAllForUsername}
                                  />
                                  <label
                                    style={{
                                      marginBottom: "0px",
                                      fontWeight: "400",
                                      fontSize: '13px',
                                      cursor: 'pointer',
                                    }}
                                    onClick={() => handleSelectAllForUsername()}>
                                    Select all
                                  </label>
                                </li>
                                <li>
                                  {uniqueDataUsernames
                                    .slice()
                                    .filter((name) => name !== undefined)
                                    .sort((a, b) => {
                                      // const array2 = ["kiwi", "papaya", "orange"]; // Replace this with your actual array
                                      const trimmedA = a?.trim().toLowerCase();
                                      const trimmedB = b?.trim().toLowerCase();

                                      const inArray2A = usernameSelected.includes(trimmedA);
                                      const inArray2B = usernameSelected.includes(trimmedB);

                                      if (inArray2A && !inArray2B) {
                                        return -1;
                                      } else if (!inArray2A && inArray2B) {
                                        return 1;
                                      } else {
                                        return trimmedA.localeCompare(trimmedB);
                                      }
                                    })
                                    .map((username, index) => (
                                      <div
                                        key={index}
                                        className="filter-inputs"
                                      >
                                        <input
                                          type="checkbox"
                                          style={{ width: "12px" }}
                                          checked={usernameSelected.includes(
                                            username.toLowerCase(),
                                          )}
                                          onChange={() =>
                                            handleCheckboxChangeUsername(
                                              username.toLowerCase(),
                                            )
                                          }
                                        />
                                        <label style={{ marginBottom: "0px", cursor: 'pointer' }}
                                          onClick={() => handleCheckboxChangeUsername(
                                            username.toLowerCase(),
                                          )}>
                                          {username}
                                        </label>
                                      </div>
                                    ))}
                                </li>
                              </ul>
                            </form>
                            {/* <div className="filter-popup-footer">
                              <button onClick={handleOkClick}>OK</button>
                              <button
                                onClick={() => {
                                  setshowSearchUseraccount((prev) =>
                                    Object.fromEntries(
                                      Object.entries(prev).map(
                                        ([key, value]) => [key, false],
                                      ),
                                    ),
                                  );
                                }}
                              >
                                Cancel
                              </button>
                            </div> */}
                          </div>
                        )}
                      </th>

                      {/* Similarly, you can add filter popups for name, email, user_type, and is_verified */}

                      <th style={{ width: "180px", color: showSearchUseraccount.showSearchname ? "orange" : "white" }}>
                        <span
                          style={{ cursor: "pointer" }}
                          id={"name_label_ref"}
                          onClick={() => {
                            setshowSearchUseraccount((prev) => ({
                              ...Object.fromEntries(
                                Object.keys(prev).map((key) => [
                                  key,
                                  key === "showSearchname"
                                    ? !prev.showSearchname
                                    : false,
                                ]),
                              ),
                            }));
                          }}
                        >Name{" "}</span>
                        <MdFilterAlt
                          style={{ color: isnameFiltered ? "orange" : "white" }}
                          id={"name_ref"}
                          className="arrow"
                          onClick={() => {
                            setshowSearchUseraccount((prev) => ({
                              ...Object.fromEntries(
                                Object.keys(prev).map((key) => [
                                  key,
                                  key === "showSearchname"
                                    ? !prev.showSearchname
                                    : false,
                                ]),
                              ),
                            }));
                          }}
                        />
                        {showSearchUseraccount.showSearchname && (
                          <div ref={uniRef} className="Filter-popup">
                            <form
                              id="filter-form"
                              className="Filter-inputs-container"
                            >
                              <ul>
                                <li>
                                  <input
                                    type="checkbox"
                                    style={{
                                      width: "12px",
                                      marginRight: "5px",
                                    }}
                                    checked={selectAll}
                                    onChange={handleSelectAll}
                                  />
                                  <label
                                    style={{
                                      marginBottom: "0px",
                                      fontWeight: "400",
                                      fontSize: '13px',
                                      cursor: 'pointer',
                                    }}
                                    onClick={() => handleSelectAll()}>
                                    Select all
                                  </label>
                                </li>
                                <li>
                                  {uniqueDataname
                                    .slice()
                                    .filter((name) => name !== undefined)
                                    .sort((a, b) => {
                                      // const array2 = ["kiwi", "papaya", "orange"]; // Replace this with your actual array
                                      const trimmedA = a?.trim().toLowerCase();
                                      const trimmedB = b?.trim().toLowerCase();

                                      const inArray2A = nameSelected.includes(trimmedA);
                                      const inArray2B = nameSelected.includes(trimmedB);

                                      if (inArray2A && !inArray2B) {
                                        return -1;
                                      } else if (!inArray2A && inArray2B) {
                                        return 1;
                                      } else {
                                        return trimmedA.localeCompare(trimmedB);
                                      }
                                    })
                                    .map((name, index) => (
                                      <div
                                        key={index}
                                        className="filter-inputs"
                                      >
                                        <input
                                          type="checkbox"
                                          style={{ width: "12px" }}
                                          checked={nameSelected.includes(
                                            name.toLowerCase(),
                                          )}
                                          onChange={() =>
                                            handleCheckboxChangename(
                                              name.toLowerCase(),
                                            )
                                          }
                                        />
                                        <label style={{ marginBottom: "0px", cursor: 'pointer' }}
                                          onClick={() => handleCheckboxChangename(
                                            name.toLowerCase(),
                                          )}>
                                          {name}
                                        </label>
                                      </div>
                                    ))}
                                </li>
                              </ul>
                            </form>
                            {/* <div className="filter-popup-footer">
                              <button onClick={handleOkClick}>OK</button>
                              <button
                                onClick={() => {
                                  setshowSearchUseraccount((prev) =>
                                    Object.fromEntries(
                                      Object.entries(prev).map(
                                        ([key, value]) => [key, false],
                                      ),
                                    ),
                                  );
                                }}
                              >
                                Cancel
                              </button>
                            </div> */}
                          </div>
                        )}
                      </th>
                      <th style={{ width: "240px", color: showSearchUseraccount.showSearchEmail ? "orange" : "white" }}>
                        <span
                          style={{ cursor: "pointer" }}
                          id={"email_label_ref"}
                          onClick={() => {
                            setshowSearchUseraccount((prev) => ({
                              ...Object.fromEntries(
                                Object.keys(prev).map((key) => [
                                  key,
                                  key === "showSearchEmail"
                                    ? !prev.showSearchEmail
                                    : false,
                                ]),
                              ),
                            }));
                          }}
                        >Email{" "}</span>
                        <MdFilterAlt
                          style={{
                            color: isemailFiltered ? "orange" : "white",
                          }}
                          id={"email_ref"}
                          className="arrow"
                          onClick={() => {
                            setshowSearchUseraccount((prev) => ({
                              ...Object.fromEntries(
                                Object.keys(prev).map((key) => [
                                  key,
                                  key === "showSearchEmail"
                                    ? !prev.showSearchEmail
                                    : false,
                                ]),
                              ),
                            }));
                          }}
                        />
                        {showSearchUseraccount.showSearchEmail && (
                          <div
                            ref={uniRef}
                            className="Filter-popup"
                            style={{ width: "auto" }}
                          >
                            <form
                              id="filter-form"
                              className="Filter-inputs-container"
                            >
                              <ul>
                                <li>
                                  <input
                                    type="checkbox"
                                    style={{
                                      width: "12px",
                                      marginRight: "5px",
                                    }}
                                    checked={selectAllEmail}
                                    onChange={handleSelectAllForEmail}
                                  />
                                  <label
                                    onClick={() => handleSelectAllForEmail()}
                                    style={{
                                      fontSize: '13px',
                                      fontWeight: '400',
                                      cursor: 'pointer',
                                      marginBottom: "0px",

                                    }}>
                                    Select all
                                  </label>
                                </li>
                                <li>
                                  {uniqueDataEmails
                                    .slice()
                                    .sort((a, b) => {
                                      // const array2 = ["kiwi", "papaya", "orange"]; // Replace this with your actual array
                                      const trimmedA = a?.trim().toLowerCase();
                                      const trimmedB = b?.trim().toLowerCase();

                                      const inArray2A = emailSelected.includes(trimmedA);
                                      const inArray2B = emailSelected.includes(trimmedB);

                                      if (inArray2A && !inArray2B) {
                                        return -1;
                                      } else if (!inArray2A && inArray2B) {
                                        return 1;
                                      } else {
                                        return 0;
                                      }
                                    })
                                    .map((email, index) => (
                                      <div
                                        key={index}
                                        className="filter-inputs"
                                      >
                                        <input
                                          type="checkbox"
                                          style={{ width: "12px" }}
                                          checked={emailSelected.includes(
                                            email.toLowerCase(),
                                          )}
                                          onChange={() =>
                                            handleCheckboxChangeEmail(
                                              email.toLowerCase(),
                                            )
                                          }
                                        />
                                        <label style={{ marginBottom: "0px", cursor: 'pointer' }}
                                          onClick={() => handleCheckboxChangeEmail(
                                            email.toLowerCase(),
                                          )}>
                                          {email}
                                        </label>
                                      </div>
                                    ))}
                                </li>
                              </ul>
                            </form>
                            {/* <div className="filter-popup-footer">
                              <button onClick={handleOkClick}>OK</button>
                              <button
                                onClick={() => {
                                  setshowSearchUseraccount((prev) =>
                                    Object.fromEntries(
                                      Object.entries(prev).map(
                                        ([key, value]) => [key, false],
                                      ),
                                    ),
                                  );
                                }}
                              >
                                Cancel
                              </button>
                            </div> */}
                          </div>
                        )}
                      </th>
                      <th style={{ width: "100px", color: showSearchUseraccount.showSearchUsertype ? "orange" : "white" }}>
                        <span
                          style={{ cursor: "pointer" }}
                          id={"usertype_label_ref"}
                          onClick={() => {
                            setshowSearchUseraccount((prev) => ({
                              ...Object.fromEntries(
                                Object.keys(prev).map((key) => [
                                  key,
                                  key === "showSearchUsertype"
                                    ? !prev.showSearchUsertype
                                    : false,
                                ]),
                              ),
                            }));
                          }}
                        > User Type{" "}</span>
                        <MdFilterAlt
                          style={{
                            color: isusertypeFiltered ? "orange" : "white",
                          }}
                          id={"usertype_ref"}
                          className="arrow"
                          onClick={() => {
                            setshowSearchUseraccount((prev) => ({
                              ...Object.fromEntries(
                                Object.keys(prev).map((key) => [
                                  key,
                                  key === "showSearchUsertype"
                                    ? !prev.showSearchUsertype
                                    : false,
                                ]),
                              ),
                            }));
                          }}
                        />
                        {showSearchUseraccount.showSearchUsertype && (
                          <div ref={uniRef} className="Filter-popup">
                            <form
                              id="filter-form"
                              className="Filter-inputs-container"
                            >
                              <ul>
                                <li>
                                  <input
                                    type="checkbox"
                                    style={{
                                      width: "12px",
                                      marginRight: "5px",
                                    }}
                                    checked={selectAllUserType}
                                    onChange={handleSelectAllForUserType}
                                  />
                                  <label
                                    onClick={() => handleSelectAllForUserType()}
                                    style={{
                                      fontSize: '13px',
                                      fontWeight: '400',
                                      cursor: 'pointer',
                                      marginBottom: "0px",

                                    }}>
                                    Select all
                                  </label>
                                </li>
                                <li>
                                  {uniqueDataUserTypes.map(
                                    (user_type, index) => (
                                      <div
                                        key={index}
                                        className="filter-inputs"
                                      >
                                        <input
                                          type="checkbox"
                                          style={{ width: "12px" }}
                                          checked={userTypeSelected.includes(
                                            user_type.toLowerCase(),
                                          )}
                                          onChange={() =>
                                            handleCheckboxChangeUserType(
                                              user_type.toLowerCase(),
                                            )
                                          }
                                        />
                                        <label style={{ marginBottom: "0px", cursor: 'pointer' }}
                                          onClick={() => handleCheckboxChangeUserType(
                                            user_type.toLowerCase(),
                                          )}>
                                          {user_type}
                                        </label>
                                      </div>
                                    ),
                                  )}
                                </li>
                              </ul>
                            </form>
                            {/* <div className="filter-popup-footer">
                              <button onClick={handleOkClick}>OK</button>
                              <button
                                onClick={() => {
                                  setshowSearchUseraccount((prev) =>
                                    Object.fromEntries(
                                      Object.entries(prev).map(
                                        ([key, value]) => [key, false],
                                      ),
                                    ),
                                  );
                                }}
                              >
                                Cancel
                              </button>
                            </div> */}
                          </div>
                        )}
                      </th>
                      <th style={{ width: "180px" }}>Account Deactivation</th>
                      <th style={{ width: "80px" }}>Delete</th>
                      {/* <th style={{ width: "180px", color: showSearchUseraccount.showSearchdeactivatename ? "orange" : "white" }}>
                        <span
                          style={{ cursor: "pointer" }}
                          id={"deactivename_label_Ref"}
                          onClick={() => {
                            setshowSearchUseraccount((prev) => ({
                              ...Object.fromEntries(
                                Object.keys(prev).map((key) => [
                                  key,
                                  key === "showSearchdeactivatename"
                                    ? !prev.showSearchdeactivatename
                                    : false,
                                ]),
                              ),
                            }));
                          }}
                        >Deactivate By{" "}</span>
                        <MdFilterAlt
                          style={{ color: isdeactivenameFiltered ? "orange" : "white" }}
                          id={"deactivename_ref"}
                          className="arrow"
                          onClick={() => {
                            setshowSearchUseraccount((prev) => ({
                              ...Object.fromEntries(
                                Object.keys(prev).map((key) => [
                                  key,
                                  key === "showSearchdeactivatename"
                                    ? !prev.showSearchdeactivatename
                                    : false,
                                ]),
                              ),
                            }));
                          }}
                        />
                        {showSearchUseraccount.showSearchdeactivatename && (
                          <div ref={uniRef} className="Filter-popup" style={{marginLeft:"10%"}}>
                            <form
                              id="filter-form"
                              className="Filter-inputs-container"
                            >
                              <ul>
                                <li>
                                  <input
                                    type="checkbox"
                                    style={{
                                      width: "12px",
                                      marginRight: "5px",
                                    }}
                                    checked={selectAll}
                                    onChange={handleSelectAll}
                                  />
                                  <label
                                    style={{
                                      marginBottom: "0px",
                                      fontWeight: "400",
                                      fontSize: '13px',
                                      cursor: 'pointer',
                                    }}
                                    onClick={() => handleSelectdeactivenameAll()}>
                                    Select all
                                  </label>
                                </li>
                                <li>
                                  {uniqueDatadeactivename
                                    .slice()
                                    .filter((deactivated_by) => deactivated_by !== undefined)
                                    .sort((a, b) => {
                                    
                                      const trimmedA = a?.trim().toLowerCase();
                                      const trimmedB = b?.trim().toLowerCase();

                                      const inArray2A = deactivenameSelected.includes(trimmedA);
                                      const inArray2B = deactivenameSelected.includes(trimmedB);

                                      if (inArray2A && !inArray2B) {
                                        return -1;
                                      } else if (!inArray2A && inArray2B) {
                                        return 1;
                                      } else {
                                        return trimmedA.localeCompare(trimmedB);
                                      }
                                    })
                                    .map((deactivated_by, index) => (
                                      <div
                                        key={index}
                                        className="filter-inputs"
                                      >
                                        <input
                                          type="checkbox"
                                          style={{ width: "12px" }}
                                          checked={deactivenameSelected.includes(
                                            deactivated_by?.toLowerCase()||'',
                                          )}
                                          onChange={() =>
                                            handleCheckboxChangedeactivename(
                                              deactivated_by?.toLowerCase()||'',
                                            )
                                          }
                                        />
                                        <label style={{ marginBottom: "0px", cursor: 'pointer' }}
                                          onClick={() => handleCheckboxChangedeactivename(
                                            deactivated_by.toLowerCase(),
                                          )}>
                                          {deactivated_by}
                                        </label>
                                      </div>
                                    ))}
                                </li>
                              </ul>
                            </form>
                           
                          </div>
                        )}
                      </th> */}
                    </tr>
                  </thead>
                  <tbody className="td_UA">
                    {displayItems()?.map((user, index) => (
                      <tr
                        className="candidate-row fade-in-element slide-in-element row-animation-delay"
                        key={index}
                      >
                        <td style={{ padding: "5px" }}>{user.username}</td>
                        <td style={{ textAlign: "left",
                                //backgroundColor: selectedJobs.includes(item.id) && item.client === currentClient ? '#d3f9d8' : '',

                                padding: "5px",
                                borderBottom: "1px solid #ddd",
                                whiteSpace: "normal",
                                wordWrap: "break-word",}}>{user.name}</td>
                        <td style={{ paddingLeft: "5px" }}>{user.email}</td>
                        <td style={{ textAlign: "left", padding: "5px" }}>
                          {user.user_type}
                        </td>
                        <td style={{ textAlign: "center"  }}>
                          <label className="toggle-switch">
                          <input
                              type="checkbox"
                              checked={!user.is_active}
                              onChange={() => handleOpenModal(user.username, user.is_active)}
                              disabled={toggleDisabled}
                              style={{ marginTop: "15px" }}
                            />
                            <span className="sliderAccountDeactivation round"></span>
                          </label>
                        </td>
                        <td  style={{
    textAlign: "center",
    opacity: user.is_active ? 0.5 : 1, // Reduce opacity when checkbox is unchecked
    cursor: user.is_active ? "not-allowed" : "pointer", // Disable cursor
    pointerEvents: user.is_active ? "none" : "auto", // Prevent click events when disabled
  }}
  onClick={() => deleteFunction(user.id)}
  > <FontAwesomeIcon
                                  icon={faTrashAlt}
                                  style={{ color:user.is_active ? "grey": "#E15554", cursor: "pointer", fontSize: "18px" }}
                                  onClick={() => deleteFunction(user.id)}
                                /></td>
                        {/* <td style={{  textAlign: "left", paddingLeft: "20px" }}>
                        {user.deactivated_by ? user.deactivated_by : '-'}
                        </td> */}
                      </tr>
                    ))}
                  </tbody>
                </table>
                {displayItems()?.length === 0 && (
                  <div
                    style={{
                      backgroundColor: "",
                      textAlign: "center",
                      padding: "10px 0px 10px 0px",
                    }}
                  >
                    No data availible in table
                  </div>
                )}
              </div>
            </div>
            <div
              className="dashbottom"
             
            >
              <div>
                Showing {belowCount === 0 ? 0 : (id - 1) * 30 + 1} to{" "}
                {id * 30 <= belowCount ? id * 30 : belowCount} of {belowCount}{" "}
                entries
              </div>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  marginTop: "10px",
                }}
                className="pagination"
              >
                <ul className="page">
                <li
                className="page__btn newpage_btn"
                style={{
                  padding: "1px 5px",
                  marginRight: "5px",
                  cursor: "pointer",
                  alignItems: "center",
                  color: "#32406d",
                }}
                onClick={() => {
                  if (id !== 1) {
                    setId(id - 1);  // Go to previous page if not the first page
                  } else {
                    toast.warn("You have reached the starting page already.", {
                      position: "top-right",
                      autoClose: 3000,
                      hideProgressBar: false,
                      closeOnClick: true,
                      pauseOnHover: true,
                      draggable: true,
                      progress: undefined,
                      theme: "dark",
                      transition: Bounce,
                    });  // Show warning toast if already on first page
                  }
                }}
              >
                <FaAngleLeft style={{ marginTop: "3px" }} />
              </li>
                  <div style={{ display: "flex", columnGap: "10px" }}>
                    <div>
                      {getPageRange().map((pageNumber, index) => (
                        <button
                          className={
                            pageNumber === id ? "pag_buttons" : "unsel_button"
                          }
                          key={index}
                          onClick={() => goToPage(pageNumber)}
                          style={{
                            fontWeight: pageNumber === id ? "bold" : "normal",
                            marginRight: "10px",
                            color: pageNumber === id ? "white" : "#000000", // Changed text color
                            backgroundColor:
                              pageNumber === id ? "#32406d" : "#ffff", // Changed background color
                            borderRadius: pageNumber === id ? "0.2rem" : "",
                            fontSize: "15px",
                            border: "none",
                            padding: "1px 10px", // Adjusted padding
                            cursor: "pointer", // Added cursor pointer
                          }}
                          class="page__numbers"
                        >
                          {pageNumber}
                        </button>
                      ))}
                    </div>
                  </div>
                  <li
                    className="page__btn newpage_btn"
                    style={{
                      padding: "1px 5px",
                      cursor: "pointer",
                      color: "#32406d",
                      marginLeft: "3px"
                    }}
                    onClick={() => {
                      if (filteredRows.length > id * 30) setId(id + 1);
                      else {
                        toast.warn("Reached the end of the list", {
                          position: "top-right",
                          autoClose: 3000,
                          hideProgressBar: false,
                          closeOnClick: true,
                          pauseOnHover: true,
                          draggable: true,
                          progress: undefined,
                          theme: "dark",
                          transition: Bounce,
                        });
                        setId(id);
                      }
                    }}
                  >
                    <FaAngleRight style={{ marginTop: "3px" }} />
                  </li>
                </ul>
              </div>
            </div>
          </>
        )}
      </div>

      <Modal
                isOpen={showModal}
                 onRequestClose={handleCloseModal}
                contentLabel="Logout Confirmation"
                className="modal-content"
                overlayClassName="modal-overlay"
                style={{
                  overlay: {
                    backgroundColor: "rgba(0, 0, 0, 0.5)",
                    backdropFilter: "blur(0.5px)",
                    zIndex: 9999,
                    position: "fixed",
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                  },
                  content: {
                    width: "270px",
                    maxHeight: "400px",
                    margin: "auto",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                    background: "white",
                    borderRadius: "10px",
                    boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.5)",
                    padding: "20px 20px 10px",
                  },
                }}
              >
                <div className="modal-actions" style={{ marginBottom: "35px", display:"flex",justifyContent:"center" }}>
                  <p
                    style={{
                      fontSize: "17px",
                      fontFamily: "roboto",
                      fontWeight: "400",
                      padding:"10px",
                      color: "black",
                    }}
                  >
                   {modalMessage}
                  </p>
                </div>
                <div style={{ marginTop: "-40px",display:"flex",justifyItems:"space-between" }}>
                  <button
                     onClick={handleCloseModal}
                    style={{
                      marginRight: "30px",
                      backgroundColor: "green",
                      color: "white",
                      borderRadius: "5px",
                      border: "none",
                      padding: "6px",
                      cursor: "pointer",
                    }}
                  >
                    Cancel
                  </button>
                  <button
                     onClick={() => {
                      toggleUsers(selectedUser.username, selectedUser.isActive);
                      handleCloseModal();
                    }}
                    style={{
                      backgroundColor: "Red",
                      color: "white",
                      border: "none",
                      // width: "60px",
                      padding: "6px",
                      borderRadius: "5px",
                      cursor: "pointer",
                    }}
                  >
                    Confrim
                  </button>
                </div>
              </Modal>

                <Modal
                      isOpen={showModaldel}
                      onRequestClose={handleCloseModaldel}
                      contentLabel="Logout Confirmation"
              
                      className="modal-content"
                      overlayClassName="modal-overlay"
                      style={{
                        overlay: {
                          backgroundColor: "rgba(0, 0, 0, 0.5)", // Transparnt background to show blurred content
                          backdropFilter: "blur(0.5px)", // Blur effect for the entire screen
                          zIndex: 9999,
                          position: "fixed",
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                        },
                        content: {
                          width: "270px",
                          height: "110px",
                          margin: "auto",
                          display: "flex",
                          flexDirection: "column",
                          justifyContent: "center",
                          alignItems: "center",
                          background: "white",
                          borderRadius: "10px",
                          boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.5)",
                          padding: "20px 20px 10px",
                        },
                      }}
                    >
                      <div className="modal-actions" style={{ marginBottom: "10px" }}>
                        <p
                          style={{
                            fontSize: "17px",
                            fontFamily: "roboto",
                            // fontWeight: "50",
                            color: "black",
                          }}
                        >
                          Are you sure you want to Delete?
                        </p>
                      </div>
                      <div
                        style={{
                          width: "100%",
                          display: "flex",
                          justifyContent: "space-evenly",
                        }}
                      >
                        {!waitForSubmission && (
                          <button
                            onClick={handleCloseModaldel}
                            style={{
                              backgroundColor: "#d90000",
                              // marginRight: "30px",
                              color: "white",
                              height: "28px",
                              borderRadius: "5px",
                              border: "none",
                              padding: "5px",
                              cursor: "pointer",
                              width: "50px",
                            }}
                          >
                            No
                          </button>
                        )}
                        <button
                          onClick={handleDeleteCandidate}
                          style={{
                            // marginRight: "30px",
                            backgroundColor: "green",
                            color: "white",
                            height: "28px",
                            borderRadius: "5px",
                            border: "none",
                            padding: "5px",
                            cursor: "pointer",
                            width: waitForSubmission ? "70px" : "50px",
                          }}
                        >
                          {!waitForSubmission ? (
                            "Yes"
                          ) : (
                            <ThreeDots
                              wrapperClass="ovalSpinner"
                              wrapperStyle={{ marginTop: "-5px", marginLeft: "17px" }}
                              visible={waitForSubmission}
                              height="30"
                              width="30"
                              color="white"
                              ariaLabel="oval-loading"
                            />
                          )}
                        </button>
                      </div>
                    </Modal>
    </div>
  );
}

export default AccountDeactivation;
